// AUTO-GENERATED FILE - DO NOT MODIFY DIRECTLY
// Generated by proto/build-proto.js

// Import all method implementations
import { registerMethod } from "./index"
import { createRuleFile } from "./createRuleFile"
import { deleteRuleFile } from "./deleteRuleFile"
import { getRelativePaths } from "./getRelativePaths"
import { openFile } from "./openFile"
import { openImage } from "./openImage"
import { searchCommits } from "./searchCommits"
import { searchFiles } from "./searchFiles"
import { selectImages } from "./selectImages"

// Register all file service methods
export function registerAllMethods(): void {
	// Register each method with the registry
	registerMethod("createRuleFile", createRuleFile)
	registerMethod("deleteRuleFile", deleteRuleFile)
	registerMethod("getRelativePaths", getRelativePaths)
	registerMethod("openFile", openFile)
	registerMethod("openImage", openImage)
	registerMethod("searchCommits", searchCommits)
	registerMethod("searchFiles", searchFiles)
	registerMethod("selectImages", selectImages)
}
