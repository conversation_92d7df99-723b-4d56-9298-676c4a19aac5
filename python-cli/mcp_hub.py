"""MCP (Model Context Protocol) Hub for Cline CLI.

This module provides MCP server management and tool integration,
similar to the MCP functionality in the JavaScript version.
"""
import asyncio
import json
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, asdict
import logging


@dataclass
class McpServer:
    """MCP server configuration."""
    name: str
    command: str
    args: List[str]
    env: Optional[Dict[str, str]] = None
    disabled: bool = False
    timeout: int = 30
    cwd: Optional[str] = None


@dataclass
class McpTool:
    """MCP tool definition."""
    name: str
    description: str
    input_schema: Dict[str, Any]
    server_name: str


@dataclass
class McpResource:
    """MCP resource definition."""
    uri: str
    name: str
    description: Optional[str] = None
    mime_type: Optional[str] = None
    server_name: Optional[str] = None


class McpHub:
    """Hub for managing MCP servers and their tools."""
    
    def __init__(self, config_dir: Optional[str] = None):
        """Initialize the MCP hub.
        
        Args:
            config_dir: Directory for MCP configuration files
        """
        self.config_dir = Path(config_dir or "mcp_config")
        self.config_dir.mkdir(exist_ok=True)
        
        self.servers: Dict[str, McpServer] = {}
        self.server_processes: Dict[str, subprocess.Popen] = {}
        self.available_tools: Dict[str, McpTool] = {}
        self.available_resources: Dict[str, McpResource] = {}
        
        self.logger = logging.getLogger(__name__)
        
        # Load existing server configurations
        self._load_server_configs()
    
    def _load_server_configs(self) -> None:
        """Load MCP server configurations from disk."""
        config_file = self.config_dir / "servers.json"
        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    servers_data = json.load(f)
                
                for server_data in servers_data:
                    server = McpServer(**server_data)
                    self.servers[server.name] = server
                    
            except Exception as e:
                self.logger.error(f"Failed to load MCP server configs: {e}")
    
    def _save_server_configs(self) -> None:
        """Save MCP server configurations to disk."""
        config_file = self.config_dir / "servers.json"
        try:
            servers_data = [asdict(server) for server in self.servers.values()]
            with open(config_file, 'w') as f:
                json.dump(servers_data, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save MCP server configs: {e}")
    
    def add_server(self, server: McpServer) -> bool:
        """Add an MCP server.
        
        Args:
            server: MCP server configuration
            
        Returns:
            True if server was added successfully
        """
        try:
            self.servers[server.name] = server
            self._save_server_configs()
            
            # If server is not disabled, try to start it
            if not server.disabled:
                asyncio.create_task(self._start_server(server.name))
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to add MCP server {server.name}: {e}")
            return False
    
    def remove_server(self, server_name: str) -> bool:
        """Remove an MCP server.
        
        Args:
            server_name: Name of the server to remove
            
        Returns:
            True if server was removed successfully
        """
        try:
            if server_name in self.servers:
                # Stop the server if running
                asyncio.create_task(self._stop_server(server_name))
                
                # Remove from configuration
                del self.servers[server_name]
                self._save_server_configs()
                
                # Remove tools and resources from this server
                self._remove_server_tools_and_resources(server_name)
                
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to remove MCP server {server_name}: {e}")
            return False
    
    async def toggle_server_disabled_rpc(self, server_name: str, disabled: bool) -> List[McpServer]:
        """Toggle a server's disabled status.
        
        Args:
            server_name: Name of the server
            disabled: Whether to disable the server
            
        Returns:
            List of all servers after the change
        """
        if server_name in self.servers:
            self.servers[server_name].disabled = disabled
            self._save_server_configs()
            
            if disabled:
                await self._stop_server(server_name)
            else:
                await self._start_server(server_name)
        
        return list(self.servers.values())
    
    async def _start_server(self, server_name: str) -> bool:
        """Start an MCP server.
        
        Args:
            server_name: Name of the server to start
            
        Returns:
            True if server started successfully
        """
        if server_name not in self.servers:
            return False
        
        server = self.servers[server_name]
        
        try:
            # Stop existing process if running
            await self._stop_server(server_name)
            
            # Prepare environment
            env = dict(os.environ)
            if server.env:
                env.update(server.env)
            
            # Start the server process
            process = subprocess.Popen(
                [server.command] + server.args,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env,
                cwd=server.cwd,
                text=True
            )
            
            self.server_processes[server_name] = process
            
            # Initialize the server and discover tools
            await self._initialize_server(server_name)
            
            self.logger.info(f"Started MCP server: {server_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start MCP server {server_name}: {e}")
            return False
    
    async def _stop_server(self, server_name: str) -> None:
        """Stop an MCP server.
        
        Args:
            server_name: Name of the server to stop
        """
        if server_name in self.server_processes:
            try:
                process = self.server_processes[server_name]
                process.terminate()
                
                # Wait for process to terminate
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait()
                
                del self.server_processes[server_name]
                self.logger.info(f"Stopped MCP server: {server_name}")
                
            except Exception as e:
                self.logger.error(f"Failed to stop MCP server {server_name}: {e}")
    
    async def _initialize_server(self, server_name: str) -> None:
        """Initialize an MCP server and discover its capabilities.
        
        Args:
            server_name: Name of the server to initialize
        """
        if server_name not in self.server_processes:
            return
        
        try:
            process = self.server_processes[server_name]
            
            # Send initialization request
            init_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {},
                        "resources": {}
                    },
                    "clientInfo": {
                        "name": "cline-cli",
                        "version": "1.0.0"
                    }
                }
            }
            
            # Send request
            process.stdin.write(json.dumps(init_request) + "\n")
            process.stdin.flush()
            
            # Read response (simplified - real implementation would handle JSON-RPC properly)
            response_line = process.stdout.readline()
            if response_line:
                response = json.loads(response_line)
                
                # Discover tools
                await self._discover_tools(server_name)
                
                # Discover resources
                await self._discover_resources(server_name)
            
        except Exception as e:
            self.logger.error(f"Failed to initialize MCP server {server_name}: {e}")
    
    async def _discover_tools(self, server_name: str) -> None:
        """Discover tools from an MCP server.
        
        Args:
            server_name: Name of the server
        """
        if server_name not in self.server_processes:
            return
        
        try:
            process = self.server_processes[server_name]
            
            # Send tools/list request
            tools_request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list",
                "params": {}
            }
            
            process.stdin.write(json.dumps(tools_request) + "\n")
            process.stdin.flush()
            
            # Read response
            response_line = process.stdout.readline()
            if response_line:
                response = json.loads(response_line)
                
                if "result" in response and "tools" in response["result"]:
                    for tool_data in response["result"]["tools"]:
                        tool = McpTool(
                            name=tool_data["name"],
                            description=tool_data.get("description", ""),
                            input_schema=tool_data.get("inputSchema", {}),
                            server_name=server_name
                        )
                        self.available_tools[f"{server_name}:{tool.name}"] = tool
            
        except Exception as e:
            self.logger.error(f"Failed to discover tools from {server_name}: {e}")
    
    async def _discover_resources(self, server_name: str) -> None:
        """Discover resources from an MCP server.
        
        Args:
            server_name: Name of the server
        """
        if server_name not in self.server_processes:
            return
        
        try:
            process = self.server_processes[server_name]
            
            # Send resources/list request
            resources_request = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "resources/list",
                "params": {}
            }
            
            process.stdin.write(json.dumps(resources_request) + "\n")
            process.stdin.flush()
            
            # Read response
            response_line = process.stdout.readline()
            if response_line:
                response = json.loads(response_line)
                
                if "result" in response and "resources" in response["result"]:
                    for resource_data in response["result"]["resources"]:
                        resource = McpResource(
                            uri=resource_data["uri"],
                            name=resource_data.get("name", ""),
                            description=resource_data.get("description"),
                            mime_type=resource_data.get("mimeType"),
                            server_name=server_name
                        )
                        self.available_resources[resource.uri] = resource
            
        except Exception as e:
            self.logger.error(f"Failed to discover resources from {server_name}: {e}")
    
    def _remove_server_tools_and_resources(self, server_name: str) -> None:
        """Remove tools and resources from a specific server.
        
        Args:
            server_name: Name of the server
        """
        # Remove tools
        tools_to_remove = [
            key for key, tool in self.available_tools.items()
            if tool.server_name == server_name
        ]
        for key in tools_to_remove:
            del self.available_tools[key]
        
        # Remove resources
        resources_to_remove = [
            key for key, resource in self.available_resources.items()
            if resource.server_name == server_name
        ]
        for key in resources_to_remove:
            del self.available_resources[key]
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call an MCP tool.
        
        Args:
            tool_name: Name of the tool (format: "server_name:tool_name")
            arguments: Tool arguments
            
        Returns:
            Tool execution result
        """
        if tool_name not in self.available_tools:
            return {"error": f"Tool {tool_name} not found"}
        
        tool = self.available_tools[tool_name]
        server_name = tool.server_name
        
        if server_name not in self.server_processes:
            return {"error": f"Server {server_name} not running"}
        
        try:
            process = self.server_processes[server_name]
            
            # Send tool call request
            tool_request = {
                "jsonrpc": "2.0",
                "id": 4,
                "method": "tools/call",
                "params": {
                    "name": tool.name,
                    "arguments": arguments
                }
            }
            
            process.stdin.write(json.dumps(tool_request) + "\n")
            process.stdin.flush()
            
            # Read response
            response_line = process.stdout.readline()
            if response_line:
                response = json.loads(response_line)
                return response.get("result", {"error": "No result"})
            
            return {"error": "No response from server"}
            
        except Exception as e:
            return {"error": f"Failed to call tool {tool_name}: {str(e)}"}
    
    def get_available_tools(self) -> List[McpTool]:
        """Get all available MCP tools.
        
        Returns:
            List of available tools
        """
        return list(self.available_tools.values())
    
    def get_available_resources(self) -> List[McpResource]:
        """Get all available MCP resources.
        
        Returns:
            List of available resources
        """
        return list(self.available_resources.values())
    
    def get_servers(self) -> List[McpServer]:
        """Get all configured MCP servers.
        
        Returns:
            List of configured servers
        """
        return list(self.servers.values())
    
    async def shutdown(self) -> None:
        """Shutdown all MCP servers."""
        for server_name in list(self.server_processes.keys()):
            await self._stop_server(server_name)
