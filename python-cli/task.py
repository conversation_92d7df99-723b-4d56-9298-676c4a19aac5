"""Task module for Cline CLI.

This module provides the Task class for representing tasks in the Cline CLI.
"""
import os
import uuid
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime


class Task:
    """Represents a task in the Cline CLI.
    
    A task is a conversation or interaction with the assistant, including
    the history of messages and any associated metadata.
    """
    
    def __init__(
        self,
        task_id: Optional[str] = None,
        title: Optional[str] = None,
        cwd: Optional[str] = None,
    ):
        """Initialize a task.
        
        Args:
            task_id: Optional task ID (generated if not provided)
            title: Optional task title
            cwd: Optional working directory for the task
        """
        self.id = task_id or str(uuid.uuid4())
        self.title = title or f"Task {self.id[:8]}"
        self.cwd = cwd or os.getcwd()
        self.messages: List[Dict[str, Any]] = []
        self.created_at = datetime.now().isoformat()
        self.updated_at = self.created_at
        self.metadata: Dict[str, Any] = {}
    
    def add_message(self, role: str, content: str, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Add a message to the task.
        
        Args:
            role: Message role (user, assistant, system)
            content: Message content
            metadata: Optional message metadata
            
        Returns:
            The added message
        """
        message = {
            "id": str(uuid.uuid4()),
            "role": role,
            "content": content,
            "created_at": datetime.now().isoformat(),
            "metadata": metadata or {},
        }
        
        self.messages.append(message)
        self.updated_at = message["created_at"]
        
        return message
    
    def get_messages(self) -> List[Dict[str, Any]]:
        """Get all messages in the task.
        
        Returns:
            List of messages
        """
        return self.messages
    
    def get_last_message(self) -> Optional[Dict[str, Any]]:
        """Get the last message in the task.
        
        Returns:
            Last message or None if there are no messages
        """
        if not self.messages:
            return None
        
        return self.messages[-1]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the task to a dictionary.
        
        Returns:
            Dictionary representation of the task
        """
        return {
            "id": self.id,
            "title": self.title,
            "cwd": self.cwd,
            "messages": self.messages,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "metadata": self.metadata,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Task":
        """Create a task from a dictionary.
        
        Args:
            data: Dictionary containing task data
            
        Returns:
            Task instance
        """
        task = cls(
            task_id=data.get("id"),
            title=data.get("title"),
            cwd=data.get("cwd"),
        )
        
        task.messages = data.get("messages", [])
        task.created_at = data.get("created_at", task.created_at)
        task.updated_at = data.get("updated_at", task.updated_at)
        task.metadata = data.get("metadata", {})
        
        return task
