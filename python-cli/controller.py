"""Controller module for Cline CLI.

This module provides the core controller functionality for the Cline CLI,
handling requests, managing state, and coordinating between different components.
"""
from typing import Dict, Any, List, Optional, Union
import json
import os
from pathlib import Path
import platform
import asyncio

from cline_cli.core.context import Context
from cline_cli.services.api import Api<PERSON><PERSON>ider, ModelInfo
from cline_cli.utils.fs import file_exists


class Controller:
    """Main controller for Cline CLI.
    
    This class is responsible for managing the application state, handling
    requests, and coordinating between different components.
    """
    
    def __init__(self, context: Optional[Context] = None):
        """Initialize the controller.
        
        Args:
            context: Optional Context instance
        """
        self.context = context or Context()
        self.tasks = {}
        self.current_task_id = None
        self.api_provider = None
        self.workspace_tracker = None
        self.telemetry_enabled = self._get_telemetry_setting()
        
    def _get_telemetry_setting(self) -> bool:
        """Get the telemetry setting from the configuration.
        
        Returns:
            Boolean indicating whether telemetry is enabled
        """
        return self.context.config.get("telemetry_enabled", False)
    
    def initialize(self) -> None:
        """Initialize the controller and its components."""
        self._initialize_api_provider()
        self._initialize_workspace_tracker()
        
    def _initialize_api_provider(self) -> None:
        """Initialize the API provider based on configuration."""
        api_key = self.context.config.get("api_key")
        model = self.context.config.get("model", "gpt-4")
        endpoint = self.context.config.get("endpoint")
        
        if api_key:
            self.api_provider = ApiProvider(api_key, model, endpoint)
    
    def _initialize_workspace_tracker(self) -> None:
        """Initialize the workspace tracker."""
        # This would be implemented when we add workspace tracking functionality
        pass
    
    def get_state(self) -> Dict[str, Any]:
        """Get the current state of the application.
        
        Returns:
            Dictionary containing the current state
        """
        return {
            "platform": platform.system(),
            "version": self.context.config.get("version", "0.1.0"),
            "current_task_id": self.current_task_id,
            "tasks": list(self.tasks.keys()),
            "api_configured": self.api_provider is not None,
            "telemetry_enabled": self.telemetry_enabled,
        }
    
    def update_state(self, updates: Dict[str, Any]) -> None:
        """Update the application state.
        
        Args:
            updates: Dictionary containing state updates
        """
        if "telemetry_enabled" in updates:
            self.telemetry_enabled = updates["telemetry_enabled"]
            self.context.update_config({"telemetry_enabled": self.telemetry_enabled})
        
        # Handle other state updates as needed
    
    async def execute_command(self, command: str, requires_approval: bool = True, cwd: Optional[str] = None) -> str:
        """Execute a command and return the result.
        
        Args:
            command: Command to execute
            requires_approval: Whether the command requires approval
            cwd: Working directory for the command
            
        Returns:
            Command output as string
        """
        from cline_cli.tools import execute_command
        
        # In a real implementation, we would handle approval here
        if requires_approval:
            # For now, just print a message
            print(f"Command requires approval: {command}")
        
        return execute_command(command, cwd)
    
    async def read_file(self, path: str) -> str:
        """Read a file and return its contents.
        
        Args:
            path: Path to the file
            
        Returns:
            File contents as string
        """
        from cline_cli.tools import read_file
        
        return read_file(path)
    
    async def write_file(self, path: str, content: str) -> None:
        """Write content to a file.
        
        Args:
            path: Path to the file
            content: Content to write
        """
        from cline_cli.tools import write_file
        
        write_file(path, content)
    
    async def search_files(self, query: str, path: str = ".", file_extension: str = "py") -> List[Dict[str, Any]]:
        """Search for text in files.
        
        Args:
            query: Text to search for
            path: Directory to search in
            file_extension: File extension to filter by
            
        Returns:
            List of dictionaries containing search results
        """
        from cline_cli.tools import file_search
        
        results = file_search(query, path, file_extension)
        
        # Convert results to a more JSON-friendly format
        return [
            {
                "file": str(file_path),
                "line": line_num,
                "content": content,
            }
            for file_path, line_num, content in results
        ]
