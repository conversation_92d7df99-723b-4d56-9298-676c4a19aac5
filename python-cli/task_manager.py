"""Task manager module for Cline CLI.

This module provides the TaskManager class for managing tasks in the Cline CLI.
"""
import json
import os
from pathlib import Path
from typing import Dict, Any, List, Optional

from task import Task


class TaskManager:
    """Manages tasks for Cline CLI.
    
    This class is responsible for creating, loading, saving, and managing tasks.
    """
    
    def __init__(self, storage_dir: Optional[str] = None):
        """Initialize the task manager.
        
        Args:
            storage_dir: Optional directory for storing tasks
        """
        if storage_dir:
            self.storage_dir = Path(storage_dir)
        else:
            # Default to the tasks directory in the user's config
            self.storage_dir = Path.home() / ".cline" / "tasks"
            
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        self.tasks: Dict[str, Task] = {}
        self._load_tasks()
    
    def _load_tasks(self) -> None:
        """Load tasks from storage."""
        for task_file in self.storage_dir.glob("*.json"):
            try:
                with open(task_file, "r", encoding="utf-8") as f:
                    task_data = json.load(f)
                    
                task = Task.from_dict(task_data)
                self.tasks[task.id] = task
            except (json.JSOND<PERSON>ode<PERSON>rror, IOError):
                continue
    
    def _save_task(self, task: Task) -> None:
        """Save a task to storage.
        
        Args:
            task: Task to save
        """
        task_file = self.storage_dir / f"{task.id}.json"
        
        with open(task_file, "w", encoding="utf-8") as f:
            json.dump(task.to_dict(), f, indent=2)
    
    def create_task(self, title: Optional[str] = None, cwd: Optional[str] = None) -> Task:
        """Create a new task.
        
        Args:
            title: Optional task title
            cwd: Optional working directory for the task
            
        Returns:
            The created task
        """
        task = Task(title=title, cwd=cwd)
        self.tasks[task.id] = task
        self._save_task(task)
        
        return task
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """Get a task by ID.
        
        Args:
            task_id: Task ID
            
        Returns:
            Task instance or None if not found
        """
        return self.tasks.get(task_id)
    
    def list_tasks(self) -> List[Task]:
        """List all tasks.
        
        Returns:
            List of tasks
        """
        return list(self.tasks.values())
    
    def delete_task(self, task_id: str) -> bool:
        """Delete a task.
        
        Args:
            task_id: Task ID
            
        Returns:
            True if the task was deleted, False otherwise
        """
        if task_id not in self.tasks:
            return False
        
        task_file = self.storage_dir / f"{task_id}.json"
        
        if task_file.exists():
            task_file.unlink()
            
        del self.tasks[task_id]
        return True
    
    def update_task(self, task: Task) -> None:
        """Update a task.
        
        Args:
            task: Task to update
        """
        self.tasks[task.id] = task
        self._save_task(task)
    
    def add_message_to_task(
        self,
        task_id: str,
        role: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Optional[Dict[str, Any]]:
        """Add a message to a task.
        
        Args:
            task_id: Task ID
            role: Message role (user, assistant, system)
            content: Message content
            metadata: Optional message metadata
            
        Returns:
            The added message or None if the task was not found
        """
        task = self.get_task(task_id)
        
        if not task:
            return None
        
        message = task.add_message(role, content, metadata)
        self.update_task(task)
        
        return message
