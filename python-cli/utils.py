"""Utility functions for Cline CLI."""
import os
import sys
from pathlib import Path
from typing import Any, Dict, Optional
import json

def get_home_dir() -> Path:
    """Get the Cline home directory."""
    home = Path.home() / ".cline"
    home.mkdir(exist_ok=True)
    return home


def load_json_file(file_path: str) -> Dict[str, Any]:
    """Load JSON data from a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}


def save_json_file(data: Dict[str, Any], file_path: str) -> bool:
    """Save data to a JSON file."""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2)
        return True
    except (IOError, TypeError):
        return False


def is_git_repo(path: str = ".") -> bool:
    """Check if a directory is a git repository."""
    return (Path(path) / ".git").exists()


def get_git_root(path: str = ".") -> Optional[Path]:
    """Get the root directory of a git repository."""
    try:
        from git import Repo, InvalidGitRepositoryError
        try:
            repo = Repo(path, search_parent_directories=True)
            return Path(repo.working_tree_dir).resolve()
        except (InvalidGitRepositoryError, Exception):
            return None
    except ImportError:
        # Fallback to manual search if gitpython is not available
        current = Path(path).resolve()
        while current != current.parent:
            if (current / ".git").exists():
                return current
            current = current.parent
        return None


def format_size(size_in_bytes: int) -> str:
    """Format file size in a human-readable format."""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size_in_bytes < 1024.0:
            return f"{size_in_bytes:.1f} {unit}"
        size_in_bytes /= 1024.0
    return f"{size_in_bytes:.1f} PB"


def confirm(prompt: str, default: bool = False) -> bool:
    """Ask for user confirmation."""
    if not sys.stdin.isatty():
        return default
        
    while True:
        try:
            response = input(f"{prompt} [{'Y/n' if default else 'y/N'}]").strip().lower()
            if not response:
                return default
            if response in ('y', 'yes'):
                return True
            if response in ('n', 'no'):
                return False
        except (KeyboardInterrupt, EOFError):
            print()
            return False
