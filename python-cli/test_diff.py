#!/usr/bin/env python3
"""Test script for the diff viewer functionality."""

import tempfile
import os
from pathlib import Path

from diff_viewer import TerminalDiff<PERSON>iewer, show_file_diff, show_string_diff


def test_string_diff():
    """Test string diff functionality."""
    print("=== Testing String Diff ===")
    
    old_content = """def hello_world():
    print("Hello, World!")
    return True

def goodbye():
    print("Goodbye!")"""

    new_content = """def hello_world():
    print("Hello, Beautiful World!")
    return True

def goodbye():
    print("Goodbye, cruel world!")
    
def new_function():
    print("This is new!")"""

    print("\n1. Basic string diff:")
    result = show_string_diff(old_content, new_content, "example.py")
    print(result)
    
    print("\n2. Side-by-side string diff:")
    viewer = TerminalDiffViewer()
    file_diff = viewer.compare_strings(old_content, new_content, "example.py")
    side_by_side = viewer.create_side_by_side_diff(file_diff)
    print(side_by_side)


def test_file_diff():
    """Test file diff functionality."""
    print("\n=== Testing File Diff ===")
    
    # Create temporary files
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f1:
        f1.write("""class Calculator:
    def __init__(self):
        self.result = 0
    
    def add(self, x, y):
        return x + y
    
    def subtract(self, x, y):
        return x - y""")
        file1_path = f1.name
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f2:
        f2.write("""class Calculator:
    def __init__(self):
        self.result = 0
        self.history = []
    
    def add(self, x, y):
        result = x + y
        self.history.append(f"add({x}, {y}) = {result}")
        return result
    
    def subtract(self, x, y):
        result = x - y
        self.history.append(f"subtract({x}, {y}) = {result}")
        return result
    
    def multiply(self, x, y):
        result = x * y
        self.history.append(f"multiply({x}, {y}) = {result}")
        return result""")
        file2_path = f2.name
    
    try:
        print(f"\n1. File diff between {file1_path} and {file2_path}:")
        result = show_file_diff(file1_path, file2_path, context_lines=2)
        print(result)
        
        print("\n2. Side-by-side file diff:")
        viewer = TerminalDiffViewer(context_lines=2)
        file_diff = viewer.compare_files(file1_path, file2_path)
        side_by_side = viewer.create_side_by_side_diff(file_diff)
        print(side_by_side)
        
    finally:
        # Clean up temporary files
        os.unlink(file1_path)
        os.unlink(file2_path)


def test_rich_formatting():
    """Test rich formatting if available."""
    print("\n=== Testing Rich Formatting ===")
    
    try:
        from rich.console import Console
        console = Console()
        
        old_content = "Hello World"
        new_content = "Hello Beautiful World"
        
        viewer = TerminalDiffViewer(use_color=True)
        file_diff = viewer.compare_strings(old_content, new_content, "test.txt")
        
        print("\nRich-formatted diff:")
        rich_result = viewer.display_diff(file_diff)
        print(rich_result)
        
    except ImportError:
        print("Rich not available - using plain text formatting")
        
        viewer = TerminalDiffViewer(use_color=False)
        file_diff = viewer.compare_strings("Hello World", "Hello Beautiful World", "test.txt")
        
        print("\nPlain text diff:")
        plain_result = viewer.display_diff(file_diff)
        print(plain_result)


def test_diff_stats():
    """Test diff statistics."""
    print("\n=== Testing Diff Statistics ===")
    
    old_content = """Line 1
Line 2
Line 3
Line 4
Line 5"""

    new_content = """Line 1
Modified Line 2
Line 3
New Line 3.5
Line 5
New Line 6"""

    viewer = TerminalDiffViewer()
    file_diff = viewer.compare_strings(old_content, new_content, "stats_test.txt")
    
    print(f"File: {file_diff.file_path}")
    print(f"Statistics: {file_diff.stats}")
    print(f"Total diff lines: {len(file_diff.diff_lines)}")
    
    print("\nDiff output:")
    result = viewer.display_diff(file_diff)
    print(result)


def test_edge_cases():
    """Test edge cases."""
    print("\n=== Testing Edge Cases ===")
    
    viewer = TerminalDiffViewer()
    
    # Empty files
    print("\n1. Empty files:")
    file_diff = viewer.compare_strings("", "", "empty.txt")
    print(f"Empty diff result: '{viewer.display_diff(file_diff)}'")
    
    # One empty file
    print("\n2. One empty file:")
    file_diff = viewer.compare_strings("", "New content", "one_empty.txt")
    result = viewer.display_diff(file_diff)
    print(result)
    
    # Identical files
    print("\n3. Identical files:")
    content = "Same content\nLine 2\nLine 3"
    file_diff = viewer.compare_strings(content, content, "identical.txt")
    result = viewer.display_diff(file_diff)
    print(f"Identical files result: '{result}'")
    
    # Very long lines
    print("\n4. Very long lines:")
    old_long = "This is a very long line that goes on and on and on and should be truncated in side-by-side view"
    new_long = "This is a very long line that goes on and on and ON AND ON and should be truncated in side-by-side view"
    file_diff = viewer.compare_strings(old_long, new_long, "long_lines.txt")
    side_by_side = viewer.create_side_by_side_diff(file_diff)
    print(side_by_side)


def main():
    """Run all diff tests."""
    print("🧪 Testing Cline CLI Diff Viewer")
    print("=" * 50)
    
    test_string_diff()
    test_file_diff()
    test_rich_formatting()
    test_diff_stats()
    test_edge_cases()
    
    print("\n" + "=" * 50)
    print("✅ All diff tests completed!")
    print("\nTo use the diff functionality:")
    print("1. In Python code: from diff_viewer import show_file_diff, show_string_diff")
    print("2. In CLI: python -m cli diff files file1.txt file2.txt")
    print("3. In CLI: python -m cli diff git myfile.py --staged")


if __name__ == "__main__":
    main()
