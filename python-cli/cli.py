"""Main CLI module for Cline CLI."""
import os
import json
import time
import subprocess
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console
from rich.table import Table

from __init__ import __version__
from context import Context
from task import Task
from task_manager import TaskManager
from task_executor import TaskExecutor
from system import get_system_prompt
from user_prompts import get_user_prompt
from prompt_manager import PromptManager
from workspace import WorkspaceTracker
from git import get_repository_status, generate_commit_message
from telemetry import telemetry_service
from migration import run_migration
from api import ApiConfiguration, ApiProvider
from tools import (
    execute_command,
    file_search,
    read_file,
    write_file,
    list_files,
    create_directory,
    delete_file,
    diff_files,
    diff_strings,
    git_diff,
)

app = typer.Typer(name="cline", help="Cline CLI - A powerful command-line interface")
console = Console()


def version_callback(value: bool):
    """Print version and exit."""
    if value:
        console.print(f"Cline CLI Version: {__version__}")
        raise typer.Exit()


@app.callback()
def main(
    version: Optional[bool] = typer.Option(
        None, "--version", "-v", callback=version_callback, is_eager=True
    ),
):
    """Cline CLI - A powerful command-line interface with Cline capabilities."""
    pass


@app.command()
def run(command: str):
    """Execute a shell command."""
    result = execute_command(command)
    console.print(f"[bold green]Output:[/bold green]\n{result}")


@app.command()
def search(query: str, ext: str = "py", path: str = "."):
    """Search for text in files."""
    results = file_search(query, path, ext)

    if not results:
        console.print("[yellow]No results found.[/yellow]")
        return

    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("File", style="cyan")
    table.add_column("Line", style="green")
    table.add_column("Content", style="white")

    for file_path, line_num, content in results:
        table.add_row(
            str(file_path.relative_to(Path.cwd())),
            str(line_num),
            content.strip(),
        )

    console.print(table)


@app.command()
def read(path: str):
    """Read and display a file's contents."""
    try:
        content = read_file(path)
        console.print(f"[bold]Contents of {path}:[/bold]\n{content}")
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def write(path: str, content: str = ""):
    """Write content to a file."""
    try:
        if not content:
            content = typer.edit() or ""
            if not content:
                console.print("[yellow]No content provided, aborting.[/yellow]")
                return

        write_file(path, content)
        console.print(f"[green]Successfully wrote to {path}[/green]")
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def shell():
    """Start an interactive shell."""
    console.print("Cline CLI Interactive Shell")
    console.print("Type 'exit' or 'quit' to exit\n")

    context = Context()

    while True:
        try:
            command = typer.prompt("cline> ").strip()

            if command.lower() in ("exit", "quit"):
                break

            if not command:
                continue

            # Handle built-in commands
            if command.startswith("search "):
                _, *args = command.split(maxsplit=1)
                search(args[0] if args else "", path=".")
            elif command.startswith("read "):
                _, path = command.split(maxsplit=1)
                read(path)
            elif command.startswith("write "):
                _, path = command.split(maxsplit=1)
                write(path)
            elif command.startswith("run "):
                _, cmd = command.split(maxsplit=1)
                run(cmd)
            else:
                # Execute as shell command
                run(command)

        except Exception as e:
            console.print(f"[red]Error: {e}[/red]")
        except KeyboardInterrupt:
            console.print("\nUse 'exit' or 'quit' to exit")


@app.command()
def migrate(
    source: str = typer.Argument(..., help="Path to the JavaScript Cline project"),
    target: str = typer.Option(None, help="Path to the Python Cline CLI project (default: current directory)"),
):
    """Migrate from JavaScript Cline to Python Cline CLI."""
    # If target is not specified, use current directory
    if target is None:
        target = str(Path.cwd())

    console.print(f"[bold]Migrating from {source} to {target}...[/bold]")

    # Run migration
    results = run_migration(source, target)

    # Display results
    if results["success"]:
        console.print("[bold green]Migration completed successfully![/bold green]")
    else:
        console.print("[bold yellow]Migration completed with some issues.[/bold yellow]")

    # Display migrated components
    if results["migrated_components"]:
        console.print("\n[bold green]Successfully migrated:[/bold green]")
        for component in results["migrated_components"]:
            console.print(f"✓ {component}")

    # Display failed components
    if results["failed_components"]:
        console.print("\n[bold red]Failed to migrate:[/bold red]")
        for component in results["failed_components"]:
            console.print(f"✗ {component}")

    # Display messages
    if results["messages"]:
        console.print("\n[bold]Details:[/bold]")
        for message in results["messages"]:
            console.print(f"• {message}")


# Create a prompt group for prompt-related commands
prompt_app = typer.Typer(name="prompt", help="Manage prompts for Cline CLI")
app.add_typer(prompt_app)


@prompt_app.command("list")
def list_prompts():
    """List all available prompts."""
    prompt_manager = PromptManager()
    prompts = prompt_manager.list_prompts()

    if not prompts:
        console.print("[yellow]No prompts found.[/yellow]")
        return

    console.print("[bold]Available prompts:[/bold]")
    for prompt in prompts:
        console.print(f"• {prompt}")


@prompt_app.command("show")
def show_prompt(name: str):
    """Show a specific prompt."""
    prompt_manager = PromptManager()
    prompt = prompt_manager.get_prompt(name)

    if not prompt:
        console.print(f"[red]Prompt '{name}' not found.[/red]")
        return

    console.print(f"[bold]Prompt: {name}[/bold]")
    console.print(json.dumps(prompt, indent=2))


@prompt_app.command("add")
def add_prompt(
    name: str,
    content: str = typer.Option(None, help="Prompt content"),
    file: str = typer.Option(None, help="JSON file containing prompt data")
):
    """Add a new prompt."""
    if not content and not file:
        console.print("[red]Either content or file must be provided.[/red]")
        return

    prompt_manager = PromptManager()

    if file:
        try:
            with open(file, 'r', encoding='utf-8') as f:
                prompt_data = json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            console.print(f"[red]Error reading file: {e}[/red]")
            return
    else:
        prompt_data = {"content": content}

    success = prompt_manager.add_prompt(name, prompt_data)

    if success:
        console.print(f"[green]Prompt '{name}' added successfully.[/green]")
    else:
        console.print(f"[red]Failed to add prompt '{name}'.[/red]")


@prompt_app.command("remove")
def remove_prompt(name: str):
    """Remove a prompt."""
    prompt_manager = PromptManager()
    success = prompt_manager.remove_prompt(name)

    if success:
        console.print(f"[green]Prompt '{name}' removed successfully.[/green]")
    else:
        console.print(f"[red]Failed to remove prompt '{name}'.[/red]")


# Create a task group for task-related commands
task_app = typer.Typer(name="task", help="Manage tasks for Cline CLI")
app.add_typer(task_app)


@task_app.command("list")
def list_tasks():
    """List all tasks."""
    task_manager = TaskManager()
    tasks = task_manager.list_tasks()

    if not tasks:
        console.print("[yellow]No tasks found.[/yellow]")
        return

    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("ID", style="cyan")
    table.add_column("Title", style="green")
    table.add_column("Created", style="yellow")
    table.add_column("Messages", style="blue")

    for task in tasks:
        # Format the created date for display
        created_date = task.created_at.split("T")[0] if "T" in task.created_at else task.created_at

        table.add_row(
            task.id[:8],  # Show only the first 8 characters of the ID
            task.title,
            created_date,
            str(len(task.messages)),
        )

    console.print(table)


@task_app.command("create")
def create_task(title: str = typer.Option(None, help="Title for the task")):
    """Create a new task."""
    task_manager = TaskManager()
    task = task_manager.create_task(title=title)

    console.print(f"[green]Task created successfully![/green]")
    console.print(f"ID: {task.id}")
    console.print(f"Title: {task.title}")

    return task.id


@task_app.command("show")
def show_task(task_id: str):
    """Show details of a specific task."""
    task_manager = TaskManager()
    task = task_manager.get_task(task_id)

    if not task:
        console.print(f"[red]Task with ID '{task_id}' not found.[/red]")
        return

    console.print(f"[bold]Task: {task.title}[/bold]")
    console.print(f"ID: {task.id}")
    console.print(f"Created: {task.created_at}")
    console.print(f"Updated: {task.updated_at}")
    console.print(f"Working Directory: {task.cwd}")

    if task.messages:
        console.print("\n[bold]Messages:[/bold]")

        for i, message in enumerate(task.messages, 1):
            console.print(f"\n[bold]{i}. {message['role'].capitalize()}:[/bold]")
            console.print(message['content'][:500] + ("..." if len(message['content']) > 500 else ""))
    else:
        console.print("\n[yellow]No messages in this task.[/yellow]")


@task_app.command("delete")
def delete_task(task_id: str, force: bool = typer.Option(False, "--force", "-f", help="Force deletion without confirmation")):
    """Delete a task."""
    task_manager = TaskManager()
    task = task_manager.get_task(task_id)

    if not task:
        console.print(f"[red]Task with ID '{task_id}' not found.[/red]")
        return

    if not force:
        confirm = typer.confirm(f"Are you sure you want to delete task '{task.title}'?")
        if not confirm:
            console.print("[yellow]Deletion cancelled.[/yellow]")
            return

    success = task_manager.delete_task(task_id)

    if success:
        console.print(f"[green]Task '{task.title}' deleted successfully.[/green]")
    else:
        console.print(f"[red]Failed to delete task '{task.title}'.[/red]")


@task_app.command("chat")
def chat_with_task(task_id: str = typer.Option(None, help="Task ID to chat with")):
    """Start a chat with a task."""
    task_manager = TaskManager()

    # If no task ID is provided, create a new task
    if not task_id:
        title = typer.prompt("Enter a title for the new task")
        task = task_manager.create_task(title=title)
        task_id = task.id
        console.print(f"[green]Created new task: {task.title} (ID: {task_id})[/green]")
    else:
        task = task_manager.get_task(task_id)
        if not task:
            console.print(f"[red]Task with ID '{task_id}' not found.[/red]")
            return
        console.print(f"[green]Continuing chat with task: {task.title} (ID: {task_id})[/green]")

    # Display previous messages if any
    if task.messages:
        console.print("\n[bold]Previous messages:[/bold]")
        for message in task.messages:
            console.print(f"\n[bold]{message['role'].capitalize()}:[/bold]")
            console.print(message['content'])

    console.print("\n[bold]Chat started. Type 'exit' or 'quit' to end the chat.[/bold]")

    while True:
        try:
            user_input = typer.prompt("You")

            if user_input.lower() in ("exit", "quit"):
                break

            # Add user message to task
            task_manager.add_message_to_task(task_id, "user", user_input)

            # In a real implementation, this would call the language model
            # For now, we'll just echo the user's input
            response = f"This is a mock response to: {user_input}"

            console.print("\n[bold]Assistant:[/bold]")
            console.print(response)

            # Add assistant message to task
            task_manager.add_message_to_task(task_id, "assistant", response)

        except KeyboardInterrupt:
            console.print("\n[yellow]Chat ended.[/yellow]")
            break

    console.print("\n[green]Chat session saved.[/green]")


# Create a workspace group for workspace-related commands
workspace_app = typer.Typer(name="workspace", help="Manage workspaces for Cline CLI")
app.add_typer(workspace_app)


@workspace_app.command("list")
def list_workspaces():
    """List all workspaces."""
    workspace_tracker = WorkspaceTracker()
    workspaces = workspace_tracker.list_workspaces()

    if not workspaces:
        console.print("[yellow]No workspaces found.[/yellow]")
        return

    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Name", style="cyan")
    table.add_column("Path", style="green")
    table.add_column("Last Accessed", style="yellow")

    for workspace in workspaces:
        # Format the last accessed time for display
        last_accessed = time.strftime(
            "%Y-%m-%d %H:%M:%S",
            time.localtime(workspace["last_accessed"])
        )

        table.add_row(
            workspace["name"],
            workspace["path"],
            last_accessed,
        )

    console.print(table)


@workspace_app.command("add")
def add_workspace(
    path: str = typer.Argument(..., help="Path to the workspace"),
    name: str = typer.Option(None, help="Name for the workspace")
):
    """Add a workspace."""
    try:
        workspace_tracker = WorkspaceTracker()
        workspace = workspace_tracker.add_workspace(path, name)

        console.print(f"[green]Workspace added successfully![/green]")
        console.print(f"Name: {workspace['name']}")
        console.print(f"Path: {workspace['path']}")
    except ValueError as e:
        console.print(f"[red]Error: {e}[/red]")


@workspace_app.command("remove")
def remove_workspace(
    workspace_id: str,
    force: bool = typer.Option(False, "--force", "-f", help="Force removal without confirmation")
):
    """Remove a workspace."""
    workspace_tracker = WorkspaceTracker()
    workspace = workspace_tracker.get_workspace(workspace_id)

    if not workspace:
        console.print(f"[red]Workspace with ID '{workspace_id}' not found.[/red]")
        return

    if not force:
        confirm = typer.confirm(f"Are you sure you want to remove workspace '{workspace['name']}'?")
        if not confirm:
            console.print("[yellow]Removal cancelled.[/yellow]")
            return

    success = workspace_tracker.remove_workspace(workspace_id)

    if success:
        console.print(f"[green]Workspace '{workspace['name']}' removed successfully.[/green]")
    else:
        console.print(f"[red]Failed to remove workspace '{workspace['name']}'.[/red]")


@workspace_app.command("current")
def show_current_workspace():
    """Show the current workspace."""
    workspace_tracker = WorkspaceTracker()
    workspace = workspace_tracker.get_active_workspace()

    if not workspace:
        console.print("[yellow]Not in a tracked workspace.[/yellow]")
        return

    console.print(f"[bold]Current workspace: {workspace['name']}[/bold]")
    console.print(f"Path: {workspace['path']}")

    # Show Git status if available
    git_status = get_repository_status(workspace['path'])

    if git_status["is_git_repository"]:
        console.print("\n[bold]Git status:[/bold]")
        console.print(f"Branch: {git_status['branch']}")

        if git_status["modified_files"]:
            console.print("\n[yellow]Modified files:[/yellow]")
            for file in git_status["modified_files"][:5]:  # Show only the first 5 files
                console.print(f"  {file}")
            if len(git_status["modified_files"]) > 5:
                console.print(f"  ... and {len(git_status['modified_files']) - 5} more")

        if git_status["staged_files"]:
            console.print("\n[green]Staged files:[/green]")
            for file in git_status["staged_files"][:5]:  # Show only the first 5 files
                console.print(f"  {file}")
            if len(git_status["staged_files"]) > 5:
                console.print(f"  ... and {len(git_status['staged_files']) - 5} more")

            # Generate a commit message suggestion
            commit_message = generate_commit_message(workspace['path'])
            if commit_message:
                console.print("\n[bold]Suggested commit message:[/bold]")
                console.print(f"  {commit_message}")

        if git_status["untracked_files"]:
            console.print("\n[red]Untracked files:[/red]")
            for file in git_status["untracked_files"][:5]:  # Show only the first 5 files
                console.print(f"  {file}")
            if len(git_status["untracked_files"]) > 5:
                console.print(f"  ... and {len(git_status['untracked_files']) - 5} more")


# Create a git group for git-related commands
git_app = typer.Typer(name="git", help="Git integration for Cline CLI")
app.add_typer(git_app)


@git_app.command("status")
def git_status(path: str = typer.Option(".", help="Path to the Git repository")):
    """Show Git repository status."""
    git_status = get_repository_status(path)

    if not git_status["is_git_repository"]:
        console.print(f"[red]Not a Git repository: {path}[/red]")
        return

    console.print(f"[bold]Git repository: {git_status['root']}[/bold]")
    console.print(f"Branch: {git_status['branch']}")

    if git_status["modified_files"]:
        console.print("\n[yellow]Modified files:[/yellow]")
        for file in git_status["modified_files"]:
            console.print(f"  {file}")

    if git_status["staged_files"]:
        console.print("\n[green]Staged files:[/green]")
        for file in git_status["staged_files"]:
            console.print(f"  {file}")

    if git_status["untracked_files"]:
        console.print("\n[red]Untracked files:[/red]")
        for file in git_status["untracked_files"]:
            console.print(f"  {file}")


@git_app.command("suggest-commit")
def suggest_commit_message(path: str = typer.Option(".", help="Path to the Git repository")):
    """Suggest a commit message based on staged changes."""
    git_status = get_repository_status(path)

    if not git_status["is_git_repository"]:
        console.print(f"[red]Not a Git repository: {path}[/red]")
        return

    if not git_status["staged_files"]:
        console.print("[yellow]No staged changes. Stage some changes first with 'git add'.[/yellow]")
        return

    commit_message = generate_commit_message(path)

    if not commit_message:
        console.print("[yellow]Could not generate a commit message.[/yellow]")
        return

    console.print("[bold]Suggested commit message:[/bold]")
    console.print(f"  {commit_message}")

    if typer.confirm("Do you want to commit with this message?"):
        try:
            result = subprocess.run(
                ["git", "commit", "-m", commit_message],
                cwd=path,
                check=True,
                capture_output=True,
                text=True,
            )

            console.print(f"[green]{result.stdout}[/green]")
        except subprocess.SubprocessError as e:
            console.print(f"[red]Error: {e}[/red]")
            if hasattr(e, 'stderr'):
                console.print(f"[red]{e.stderr}[/red]")


# Add telemetry commands
@app.command()
def telemetry(
    enable: bool = typer.Option(None, "--enable/--disable", help="Enable or disable telemetry")
):
    """Manage telemetry settings."""
    context = Context()

    if enable is None:
        # Show current setting
        enabled = context.config.get("telemetry_enabled", False)
        status = "enabled" if enabled else "disabled"
        console.print(f"Telemetry is currently [bold]{status}[/bold].")
        console.print("Telemetry helps us improve Cline CLI by collecting anonymous usage statistics.")
        console.print("No personal data or code content is collected.")
    else:
        # Update setting
        context.update_config({"telemetry_enabled": enable})
        telemetry_service.set_enabled(enable)

        status = "enabled" if enable else "disabled"
        console.print(f"Telemetry is now [bold]{status}[/bold].")


# Create a diff group for diff-related commands
diff_app = typer.Typer(name="diff", help="Visual diff tools for Cline CLI")
app.add_typer(diff_app)


@diff_app.command("files")
def diff_files_cmd(
    file1: str = typer.Argument(..., help="Path to the first file (old version)"),
    file2: str = typer.Argument(..., help="Path to the second file (new version)"),
    context: int = typer.Option(3, "--context", "-c", help="Number of context lines"),
    side_by_side: bool = typer.Option(False, "--side-by-side", "-s", help="Show side-by-side diff")
):
    """Compare two files and show visual diff."""
    try:
        result = diff_files(file1, file2, context, side_by_side)
        console.print(result)
    except Exception as e:
        console.print(f"[red]Error: {str(e)}[/red]")


@diff_app.command("strings")
def diff_strings_cmd(
    old_content: str = typer.Argument(..., help="Original content"),
    new_content: str = typer.Argument(..., help="New content"),
    name: str = typer.Option("content", "--name", "-n", help="Virtual file name for display"),
    side_by_side: bool = typer.Option(False, "--side-by-side", "-s", help="Show side-by-side diff")
):
    """Compare two strings and show visual diff."""
    try:
        result = diff_strings(old_content, new_content, name, side_by_side)
        console.print(result)
    except Exception as e:
        console.print(f"[red]Error: {str(e)}[/red]")


@diff_app.command("git")
def git_diff_cmd(
    file_path: str = typer.Argument(..., help="Path to the file to show diff for"),
    staged: bool = typer.Option(False, "--staged", help="Show staged changes")
):
    """Show git diff for a file with visual formatting."""
    try:
        result = git_diff(file_path, staged)
        console.print(result)
    except Exception as e:
        console.print(f"[red]Error: {str(e)}[/red]")


if __name__ == "__main__":
    app()
