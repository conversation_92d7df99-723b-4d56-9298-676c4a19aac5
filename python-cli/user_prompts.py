"""User prompts for Cline CLI.

This module provides functionality for generating user prompts.
"""
import json
from typing import Dict, Any, Optional


def get_user_prompt(query: str, context: Optional[Dict[str, Any]] = None) -> str:
    """Get a user prompt with optional context.
    
    Args:
        query: User query
        context: Optional context information
        
    Returns:
        User prompt as string
    """
    prompt = query
    
    # Add context information if provided
    if context:
        prompt = f"Context:\n{json.dumps(context, indent=2)}\n\nQuery:\n{query}"
    
    return prompt


def format_code_prompt(code: str, language: str, question: str) -> str:
    """Format a prompt for code-related questions.
    
    Args:
        code: Code snippet
        language: Programming language
        question: User's question about the code
        
    Returns:
        Formatted prompt
    """
    return f"""Here is a code snippet in {language}:

```{language}
{code}
```

{question}"""


def format_error_prompt(code: str, error: str, language: str) -> str:
    """Format a prompt for error-related questions.
    
    Args:
        code: Code snippet with error
        error: Error message
        language: Programming language
        
    Returns:
        Formatted prompt
    """
    return f"""I'm getting the following error when running this {language} code:

```{language}
{code}
```

Error:
```
{error}
```

What's causing this error and how can I fix it?"""
