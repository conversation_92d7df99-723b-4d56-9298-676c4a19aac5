"""Example usage of Cline CLI programmatically."""
from pathlib import Path
from cline_cli.tools import (
    execute_command,
    file_search,
    read_file,
    write_file,
    list_files,
)

def main():
    """Run example usage of Cline CLI tools."""
    print("=== Cline CLI Example ===\n")
    
    # 1. Execute a command
    print("1. Executing 'echo hello' command:")
    result = execute_command("echo hello")
    print(f"   Result: {result.strip()}\n")
    
    # 2. Create a test file and write to it
    test_file = Path("test_example.txt")
    print(f"2. Creating test file: {test_file}")
    write_file(str(test_file), "This is a test file.\nWith multiple lines.\nAnd some content.")
    print("   File created successfully.\n")
    
    # 3. Read the test file
    print(f"3. Reading contents of {test_file}:")
    content = read_file(str(test_file))
    print("   File contents:")
    for i, line in enumerate(content.splitlines(), 1):
        print(f"   {i}: {line}")
    print()
    
    # 4. Search in files
    print("4. Searching for 'content' in current directory:")
    results = file_search("content", ".", "txt")
    print(f"   Found {len(results)} matches:")
    for file_path, line_num, line_content in results:
        print(f"   - {file_path}:{line_num}: {line_content}")
    print()
    
    # 5. List files
    print("5. Listing all .py files in current directory:")
    py_files = list_files(".", "*.py")
    for file_path in py_files:
        print(f"   - {file_path}")
    
    # Clean up
    test_file.unlink()
    print("\n=== Example complete. Cleaned up test file. ===")

if __name__ == "__main__":
    main()
