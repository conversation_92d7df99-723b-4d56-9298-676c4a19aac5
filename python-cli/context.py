"""Context management for Cline CLI."""
from pathlib import Path
from typing import Dict, Any, Optional
import json

class Context:
    """Manages context for the CLI session."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the context with optional config path."""
        self.config_path = config_path or str(Path.home() / ".cline" / "config.json")
        self.config = self._load_config()
        self.history: list[dict[str, Any]] = []
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file."""
        try:
            config_dir = Path(self.config_path).parent
            config_dir.mkdir(parents=True, exist_ok=True)
            
            if not Path(self.config_path).exists():
                return {}
                
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, OSError):
            return {}
    
    def save_config(self) -> bool:
        """Save configuration to file."""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2)
            return True
        except OSError:
            return False
    
    def update_config(self, updates: Dict[str, Any]) -> None:
        """Update configuration with new values."""
        self.config.update(updates)
        self.save_config()
    
    def add_to_history(self, command: str, result: Any) -> None:
        """Add a command and its result to history."""
        self.history.append({
            'command': command,
            'result': str(result),
            'timestamp': self._get_timestamp()
        })
    
    def get_history(self, limit: int = 10) -> list[dict[str, Any]]:
        """Get command history."""
        return self.history[-limit:]
    
    @staticmethod
    def _get_timestamp() -> str:
        """Get current timestamp in ISO format."""
        from datetime import datetime
        return datetime.now().isoformat()
