[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "cline-python-cli"
version = "0.1.0"
description = "A Python CLI version of Cline with similar capabilities"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "Apache-2.0"}
authors = [
    {name = "Cline Team"}
]
dependencies = [
    "click>=8.0.0",
    "rich>=10.0.0",
    "python-dotenv>=0.19.0",
    "typer>=0.4.0",
    "pyyaml>=6.0",
    "aiohttp>=3.8.0",
    "asyncio-throttle>=1.0.0",
    "requests>=2.28.0",
    "gitpython>=3.1.0",
    "watchdog>=2.1.0",
    "psutil>=5.9.0"
]

[project.optional-dependencies]
test = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
    "pytest-clarity>=1.0.1",
    "pytest-sugar>=0.9.7"
]
dev = [
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
    "flake8>=6.0.0",
    "pylint>=2.17.0"
]

[project.scripts]
cline = "cline_cli.cli:app"
