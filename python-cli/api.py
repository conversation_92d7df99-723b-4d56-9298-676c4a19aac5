"""API service module for Cline CLI.

This module provides classes and functions for interacting with language model APIs
and other external services.
"""
from typing import Dict, Any, List, Optional, Union, AsyncGenerator
import json
import os
import asyncio
import aiohttp
import time
from dataclasses import dataclass
from enum import Enum


class ApiProvider(Enum):
    """Supported API providers."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    OLLAMA = "ollama"
    GEMINI = "gemini"
    MISTRAL = "mistral"


@dataclass
class ModelInfo:
    """Information about a language model."""

    id: str
    name: str
    provider: str
    max_tokens: int
    supports_functions: bool = False
    supports_vision: bool = False
    supports_streaming: bool = True
    input_cost_per_token: float = 0.0
    output_cost_per_token: float = 0.0


@dataclass
class ApiConfiguration:
    """Configuration for API access."""

    api_key: str
    model: str
    provider: str
    endpoint: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: float = 0.7
    top_p: float = 1.0


class ApiHandler:
    """Base class for API handlers."""

    def __init__(self, config: ApiConfiguration):
        """Initialize the API handler.

        Args:
            config: API configuration
        """
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None

    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()

    async def create_completion(
        self,
        messages: List[Dict[str, Any]],
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = False
    ) -> Union[Dict[str, Any], AsyncGenerator[Dict[str, Any], None]]:
        """Create a completion.

        Args:
            messages: List of messages
            tools: Optional list of tools
            stream: Whether to stream the response

        Returns:
            Completion response or async generator for streaming
        """
        raise NotImplementedError("Subclasses must implement create_completion")


class OpenAIHandler(ApiHandler):
    """Handler for OpenAI API."""

    def __init__(self, config: ApiConfiguration):
        """Initialize the OpenAI handler."""
        super().__init__(config)
        self.base_url = config.endpoint or "https://api.openai.com/v1"

    async def create_completion(
        self,
        messages: List[Dict[str, Any]],
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = False
    ) -> Union[Dict[str, Any], AsyncGenerator[Dict[str, Any], None]]:
        """Create a completion using OpenAI API."""
        headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": self.config.model,
            "messages": messages,
            "temperature": self.config.temperature,
            "top_p": self.config.top_p,
            "stream": stream
        }

        if self.config.max_tokens:
            payload["max_tokens"] = self.config.max_tokens

        if tools:
            payload["tools"] = tools
            payload["tool_choice"] = "auto"

        if not self.session:
            raise RuntimeError("Session not initialized. Use async context manager.")

        if stream:
            return self._stream_completion(headers, payload)
        else:
            return await self._single_completion(headers, payload)

    async def _single_completion(self, headers: Dict[str, str], payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle single completion request."""
        async with self.session.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=payload
        ) as response:
            if response.status != 200:
                error_text = await response.text()
                raise Exception(f"OpenAI API error {response.status}: {error_text}")

            return await response.json()

    async def _stream_completion(self, headers: Dict[str, str], payload: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Handle streaming completion request."""
        async with self.session.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=payload
        ) as response:
            if response.status != 200:
                error_text = await response.text()
                raise Exception(f"OpenAI API error {response.status}: {error_text}")

            async for line in response.content:
                line = line.decode('utf-8').strip()
                if line.startswith('data: '):
                    data = line[6:]
                    if data == '[DONE]':
                        break
                    try:
                        chunk = json.loads(data)
                        yield chunk
                    except json.JSONDecodeError:
                        continue


class AnthropicHandler(ApiHandler):
    """Handler for Anthropic API."""

    def __init__(self, config: ApiConfiguration):
        """Initialize the Anthropic handler."""
        super().__init__(config)
        self.base_url = config.endpoint or "https://api.anthropic.com"

    async def create_completion(
        self,
        messages: List[Dict[str, Any]],
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = False
    ) -> Union[Dict[str, Any], AsyncGenerator[Dict[str, Any], None]]:
        """Create a completion using Anthropic API."""
        headers = {
            "x-api-key": self.config.api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }

        # Convert messages to Anthropic format
        system_message = None
        formatted_messages = []

        for msg in messages:
            if msg["role"] == "system":
                system_message = msg["content"]
            else:
                formatted_messages.append(msg)

        payload = {
            "model": self.config.model,
            "messages": formatted_messages,
            "max_tokens": self.config.max_tokens or 4096,
            "temperature": self.config.temperature,
            "top_p": self.config.top_p,
            "stream": stream
        }

        if system_message:
            payload["system"] = system_message

        if tools:
            payload["tools"] = tools

        if not self.session:
            raise RuntimeError("Session not initialized. Use async context manager.")

        if stream:
            return self._stream_completion(headers, payload)
        else:
            return await self._single_completion(headers, payload)

    async def _single_completion(self, headers: Dict[str, str], payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle single completion request."""
        async with self.session.post(
            f"{self.base_url}/v1/messages",
            headers=headers,
            json=payload
        ) as response:
            if response.status != 200:
                error_text = await response.text()
                raise Exception(f"Anthropic API error {response.status}: {error_text}")

            return await response.json()

    async def _stream_completion(self, headers: Dict[str, str], payload: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Handle streaming completion request."""
        async with self.session.post(
            f"{self.base_url}/v1/messages",
            headers=headers,
            json=payload
        ) as response:
            if response.status != 200:
                error_text = await response.text()
                raise Exception(f"Anthropic API error {response.status}: {error_text}")

            async for line in response.content:
                line = line.decode('utf-8').strip()
                if line.startswith('data: '):
                    data = line[6:]
                    if data == '[DONE]':
                        break
                    try:
                        chunk = json.loads(data)
                        yield chunk
                    except json.JSONDecodeError:
                        continue


def create_api_handler(config: ApiConfiguration) -> ApiHandler:
    """Create an API handler based on the provider.

    Args:
        config: API configuration

    Returns:
        Appropriate API handler instance
    """
    if config.provider == ApiProvider.OPENAI.value:
        return OpenAIHandler(config)
    elif config.provider == ApiProvider.ANTHROPIC.value:
        return AnthropicHandler(config)
    else:
        raise ValueError(f"Unsupported provider: {config.provider}")


def get_model_info(model_id: str) -> Optional[ModelInfo]:
    """Get information about a model.

    Args:
        model_id: Model identifier

    Returns:
        ModelInfo object or None if not found
    """
    models = {
        # OpenAI models
        "gpt-4": ModelInfo(
            id="gpt-4",
            name="GPT-4",
            provider="openai",
            max_tokens=8192,
            supports_functions=True,
            supports_vision=False,
            supports_streaming=True,
            input_cost_per_token=0.00003,
            output_cost_per_token=0.00006,
        ),
        "gpt-4-turbo": ModelInfo(
            id="gpt-4-turbo",
            name="GPT-4 Turbo",
            provider="openai",
            max_tokens=128000,
            supports_functions=True,
            supports_vision=True,
            supports_streaming=True,
            input_cost_per_token=0.00001,
            output_cost_per_token=0.00003,
        ),
        "gpt-3.5-turbo": ModelInfo(
            id="gpt-3.5-turbo",
            name="GPT-3.5 Turbo",
            provider="openai",
            max_tokens=16385,
            supports_functions=True,
            supports_vision=False,
            supports_streaming=True,
            input_cost_per_token=0.0000015,
            output_cost_per_token=0.000002,
        ),
        # Anthropic models
        "claude-3-opus-20240229": ModelInfo(
            id="claude-3-opus-20240229",
            name="Claude 3 Opus",
            provider="anthropic",
            max_tokens=200000,
            supports_functions=True,
            supports_vision=True,
            supports_streaming=True,
            input_cost_per_token=0.000015,
            output_cost_per_token=0.000075,
        ),
        "claude-3-sonnet-20240229": ModelInfo(
            id="claude-3-sonnet-20240229",
            name="Claude 3 Sonnet",
            provider="anthropic",
            max_tokens=200000,
            supports_functions=True,
            supports_vision=True,
            supports_streaming=True,
            input_cost_per_token=0.000003,
            output_cost_per_token=0.000015,
        ),
        "claude-3-haiku-20240307": ModelInfo(
            id="claude-3-haiku-20240307",
            name="Claude 3 Haiku",
            provider="anthropic",
            max_tokens=200000,
            supports_functions=True,
            supports_vision=True,
            supports_streaming=True,
            input_cost_per_token=0.00000025,
            output_cost_per_token=0.00000125,
        ),
    }

    return models.get(model_id)


def calculate_cost(model_id: str, input_tokens: int, output_tokens: int) -> float:
    """Calculate the cost for a completion.

    Args:
        model_id: Model identifier
        input_tokens: Number of input tokens
        output_tokens: Number of output tokens

    Returns:
        Total cost in USD
    """
    model_info = get_model_info(model_id)
    if not model_info:
        return 0.0

    input_cost = input_tokens * model_info.input_cost_per_token
    output_cost = output_tokens * model_info.output_cost_per_token

    return input_cost + output_cost
