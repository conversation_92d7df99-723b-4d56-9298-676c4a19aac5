"""Telemetry service for Cline CLI.

This module provides functionality for collecting anonymous usage statistics
to help improve the Cline CLI. Telemetry is opt-in and can be disabled at any time.
"""
from typing import Dict, Any, List, Optional, Union
import json
import os
import platform
import uuid
import time
from pathlib import Path
import atexit
import threading
import queue
import logging


class TelemetryService:
    """Service for collecting anonymous usage statistics.
    
    This service collects anonymous usage statistics to help improve the Cline CLI.
    Telemetry is opt-in and can be disabled at any time.
    """
    
    def __init__(self, enabled: bool = False, storage_path: Optional[str] = None):
        """Initialize the telemetry service.
        
        Args:
            enabled: Whether telemetry is enabled
            storage_path: Optional path to store telemetry data
        """
        self.enabled = enabled
        
        if storage_path:
            self.storage_path = Path(storage_path)
        else:
            # Default to the telemetry directory in the user's config
            self.storage_path = Path.home() / ".cline" / "telemetry"
            
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # Generate a unique anonymous ID for this installation
        self.user_id = self._get_or_create_user_id()
        
        # Queue for events to be processed
        self.event_queue = queue.Queue()
        
        # Start the background thread for processing events
        self.processing_thread = None
        self.stop_event = threading.Event()
        
        if self.enabled:
            self._start_processing_thread()
            
        # Register the shutdown handler
        atexit.register(self._shutdown)
    
    def _get_or_create_user_id(self) -> str:
        """Get or create a unique anonymous ID for this installation.
        
        Returns:
            Unique anonymous ID
        """
        id_file = self.storage_path / "user_id.txt"
        
        if id_file.exists():
            try:
                return id_file.read_text().strip()
            except (IOError, UnicodeDecodeError):
                pass
                
        # Generate a new ID
        user_id = str(uuid.uuid4())
        
        try:
            id_file.write_text(user_id)
        except IOError:
            pass
            
        return user_id
    
    def _start_processing_thread(self) -> None:
        """Start the background thread for processing events."""
        if self.processing_thread is not None and self.processing_thread.is_alive():
            return
            
        self.stop_event.clear()
        self.processing_thread = threading.Thread(
            target=self._process_events,
            daemon=True,
        )
        self.processing_thread.start()
    
    def _process_events(self) -> None:
        """Process events from the queue."""
        while not self.stop_event.is_set():
            try:
                # Get an event from the queue with a timeout
                event = self.event_queue.get(timeout=1.0)
                
                # Save the event to disk
                self._save_event(event)
                
                # Mark the event as processed
                self.event_queue.task_done()
            except queue.Empty:
                # No events in the queue, continue waiting
                continue
            except Exception as e:
                # Log the error and continue
                logging.error(f"Error processing telemetry event: {e}")
                continue
    
    def _save_event(self, event: Dict[str, Any]) -> None:
        """Save an event to disk.
        
        Args:
            event: Event to save
        """
        # Generate a filename based on the timestamp
        timestamp = event.get("timestamp", int(time.time()))
        filename = f"{timestamp}_{uuid.uuid4()}.json"
        
        # Save the event to disk
        try:
            with open(self.storage_path / filename, "w", encoding="utf-8") as f:
                json.dump(event, f)
        except IOError:
            # Silently ignore errors
            pass
    
    def _shutdown(self) -> None:
        """Shutdown the telemetry service."""
        if self.processing_thread is not None and self.processing_thread.is_alive():
            # Signal the thread to stop
            self.stop_event.set()
            
            # Wait for the thread to finish (with a timeout)
            self.processing_thread.join(timeout=2.0)
    
    def set_enabled(self, enabled: bool) -> None:
        """Enable or disable telemetry.
        
        Args:
            enabled: Whether telemetry should be enabled
        """
        if self.enabled == enabled:
            return
            
        self.enabled = enabled
        
        if enabled:
            self._start_processing_thread()
        else:
            self.stop_event.set()
            if self.processing_thread is not None and self.processing_thread.is_alive():
                self.processing_thread.join(timeout=2.0)
    
    def track_event(self, event_name: str, properties: Optional[Dict[str, Any]] = None) -> None:
        """Track an event.
        
        Args:
            event_name: Name of the event
            properties: Optional properties for the event
        """
        if not self.enabled:
            return
            
        event = {
            "event": event_name,
            "properties": properties or {},
            "timestamp": int(time.time()),
            "user_id": self.user_id,
            "platform": platform.system(),
            "python_version": platform.python_version(),
        }
        
        # Add the event to the queue
        try:
            self.event_queue.put(event, block=False)
        except queue.Full:
            # Queue is full, drop the event
            pass
    
    def track_command(self, command: str, duration_ms: Optional[int] = None) -> None:
        """Track a command execution.
        
        Args:
            command: Command that was executed
            duration_ms: Optional duration of the command execution in milliseconds
        """
        properties = {
            "command": command,
        }
        
        if duration_ms is not None:
            properties["duration_ms"] = duration_ms
            
        self.track_event("command_executed", properties)
    
    def track_error(self, error: str, command: Optional[str] = None) -> None:
        """Track an error.
        
        Args:
            error: Error message
            command: Optional command that caused the error
        """
        properties = {
            "error": error,
        }
        
        if command is not None:
            properties["command"] = command
            
        self.track_event("error_occurred", properties)
    
    def track_feature_usage(self, feature: str) -> None:
        """Track feature usage.
        
        Args:
            feature: Feature that was used
        """
        self.track_event("feature_used", {"feature": feature})


# Create a singleton instance
telemetry_service = TelemetryService()
