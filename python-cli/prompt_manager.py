"""Prompt manager for Cline CLI.

This module provides functionality for managing and using prompts in the Cline CLI.
"""
from pathlib import Path
import json
from typing import Dict, Any, List, Optional

class PromptManager:
    """Manages prompts for Cline CLI."""
    
    def __init__(self, prompts_dir: Optional[str] = None):
        """Initialize the prompt manager.
        
        Args:
            prompts_dir: Directory containing prompt files (optional)
        """
        if prompts_dir:
            self.prompts_dir = Path(prompts_dir)
        else:
            # Default to the prompts directory in the package
            self.prompts_dir = Path(__file__).parent
            
        self.prompts: Dict[str, Dict[str, Any]] = {}
        self._load_prompts()
    
    def _load_prompts(self) -> None:
        """Load all prompt files from the prompts directory."""
        if not self.prompts_dir.exists():
            return
            
        for prompt_file in self.prompts_dir.glob("**/*.json"):
            try:
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    prompt_data = json.load(f)
                    
                # Use the filename (without extension) as the prompt key
                prompt_key = prompt_file.stem
                self.prompts[prompt_key] = prompt_data
            except (json.JSONDecodeError, IOError):
                continue
    
    def get_prompt(self, name: str) -> Optional[Dict[str, Any]]:
        """Get a prompt by name.
        
        Args:
            name: Name of the prompt
            
        Returns:
            Prompt data or None if not found
        """
        return self.prompts.get(name)
    
    def list_prompts(self) -> List[str]:
        """List all available prompts.
        
        Returns:
            List of prompt names
        """
        return list(self.prompts.keys())
    
    def add_prompt(self, name: str, prompt_data: Dict[str, Any]) -> bool:
        """Add a new prompt.
        
        Args:
            name: Name for the prompt
            prompt_data: Prompt data
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure prompts directory exists
            self.prompts_dir.mkdir(parents=True, exist_ok=True)
            
            # Create prompt file
            prompt_file = self.prompts_dir / f"{name}.json"
            with open(prompt_file, 'w', encoding='utf-8') as f:
                json.dump(prompt_data, f, indent=2)
                
            # Add to in-memory prompts
            self.prompts[name] = prompt_data
            return True
        except Exception:
            return False
    
    def remove_prompt(self, name: str) -> bool:
        """Remove a prompt.
        
        Args:
            name: Name of the prompt to remove
            
        Returns:
            True if successful, False otherwise
        """
        if name not in self.prompts:
            return False
            
        try:
            prompt_file = self.prompts_dir / f"{name}.json"
            if prompt_file.exists():
                prompt_file.unlink()
                
            # Remove from in-memory prompts
            del self.prompts[name]
            return True
        except Exception:
            return False
    
    def update_prompt(self, name: str, prompt_data: Dict[str, Any]) -> bool:
        """Update an existing prompt.
        
        Args:
            name: Name of the prompt to update
            prompt_data: New prompt data
            
        Returns:
            True if successful, False otherwise
        """
        # Remove existing prompt
        self.remove_prompt(name)
        
        # Add new prompt
        return self.add_prompt(name, prompt_data)
