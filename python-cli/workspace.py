"""Workspace tracking module for Cline CLI.

This module provides functionality for tracking and managing workspaces.
"""
from typing import Dict, Any, List, Optional, Set
import os
from pathlib import Path
import json
import time


class WorkspaceTracker:
    """Tracks and manages workspaces for Cline CLI.
    
    A workspace is a directory containing a project that the user is working on.
    This class helps track which workspaces are active and provides information
    about them.
    """
    
    def __init__(self, storage_path: Optional[str] = None):
        """Initialize the workspace tracker.
        
        Args:
            storage_path: Optional path to store workspace data
        """
        if storage_path:
            self.storage_path = Path(storage_path)
        else:
            # Default to the workspaces file in the user's config
            self.storage_path = Path.home() / ".cline" / "workspaces.json"
            
        self.workspaces: Dict[str, Dict[str, Any]] = {}
        self._load_workspaces()
    
    def _load_workspaces(self) -> None:
        """Load workspaces from storage."""
        if not self.storage_path.exists():
            return
            
        try:
            with open(self.storage_path, "r", encoding="utf-8") as f:
                self.workspaces = json.load(f)
        except (json.<PERSON><PERSON>r, IOError):
            self.workspaces = {}
    
    def _save_workspaces(self) -> None:
        """Save workspaces to storage."""
        # Ensure the parent directory exists
        self.storage_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(self.storage_path, "w", encoding="utf-8") as f:
            json.dump(self.workspaces, f, indent=2)
    
    def add_workspace(self, path: str, name: Optional[str] = None) -> Dict[str, Any]:
        """Add a workspace.
        
        Args:
            path: Path to the workspace
            name: Optional name for the workspace
            
        Returns:
            Dictionary containing workspace information
        """
        workspace_path = Path(path).resolve()
        
        if not workspace_path.exists() or not workspace_path.is_dir():
            raise ValueError(f"Invalid workspace path: {path}")
            
        workspace_id = str(workspace_path)
        workspace_name = name or workspace_path.name
        
        workspace = {
            "id": workspace_id,
            "name": workspace_name,
            "path": str(workspace_path),
            "added_at": time.time(),
            "last_accessed": time.time(),
        }
        
        self.workspaces[workspace_id] = workspace
        self._save_workspaces()
        
        return workspace
    
    def remove_workspace(self, workspace_id: str) -> bool:
        """Remove a workspace.
        
        Args:
            workspace_id: Workspace ID
            
        Returns:
            True if the workspace was removed, False otherwise
        """
        if workspace_id not in self.workspaces:
            return False
            
        del self.workspaces[workspace_id]
        self._save_workspaces()
        
        return True
    
    def get_workspace(self, workspace_id: str) -> Optional[Dict[str, Any]]:
        """Get a workspace by ID.
        
        Args:
            workspace_id: Workspace ID
            
        Returns:
            Dictionary containing workspace information or None if not found
        """
        return self.workspaces.get(workspace_id)
    
    def list_workspaces(self) -> List[Dict[str, Any]]:
        """List all workspaces.
        
        Returns:
            List of dictionaries containing workspace information
        """
        return list(self.workspaces.values())
    
    def get_active_workspace(self) -> Optional[Dict[str, Any]]:
        """Get the active workspace based on the current directory.
        
        Returns:
            Dictionary containing workspace information or None if not found
        """
        current_path = Path.cwd().resolve()
        
        # Check if the current directory is in any workspace
        for workspace_id, workspace in self.workspaces.items():
            workspace_path = Path(workspace["path"])
            
            if current_path == workspace_path or current_path.is_relative_to(workspace_path):
                # Update last accessed time
                workspace["last_accessed"] = time.time()
                self._save_workspaces()
                
                return workspace
                
        return None
    
    def get_workspace_files(self, workspace_id: str, pattern: str = "*") -> List[str]:
        """Get files in a workspace matching a pattern.
        
        Args:
            workspace_id: Workspace ID
            pattern: Glob pattern to match
            
        Returns:
            List of file paths
        """
        workspace = self.get_workspace(workspace_id)
        
        if not workspace:
            return []
            
        workspace_path = Path(workspace["path"])
        
        return [
            str(file_path.relative_to(workspace_path))
            for file_path in workspace_path.glob(pattern)
            if file_path.is_file()
        ]
    
    def get_workspace_directories(self, workspace_id: str) -> List[str]:
        """Get directories in a workspace.
        
        Args:
            workspace_id: Workspace ID
            
        Returns:
            List of directory paths
        """
        workspace = self.get_workspace(workspace_id)
        
        if not workspace:
            return []
            
        workspace_path = Path(workspace["path"])
        
        return [
            str(dir_path.relative_to(workspace_path))
            for dir_path in workspace_path.glob("*")
            if dir_path.is_dir() and not dir_path.name.startswith(".")
        ]
