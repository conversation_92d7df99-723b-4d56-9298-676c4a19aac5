"""Task execution engine for Cline CLI.

This module provides the core task execution functionality, including
tool execution, message parsing, and conversation management.
"""
import asyncio
import json
import re
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass
from enum import Enum

from api import ApiConfiguration, create_api_handler
from tools import (
    execute_command,
    read_file,
    write_file,
    file_search,
    list_files,
)


class ToolType(Enum):
    """Available tool types."""
    EXECUTE_COMMAND = "execute_command"
    READ_FILE = "read_file"
    WRITE_FILE = "write_file"
    FILE_SEARCH = "file_search"
    LIST_FILES = "list_files"


@dataclass
class ToolCall:
    """Represents a tool call from the assistant."""
    name: str
    arguments: Dict[str, Any]
    id: Optional[str] = None


@dataclass
class ToolResult:
    """Represents the result of a tool execution."""
    tool_call_id: Optional[str]
    content: str
    success: bool = True
    error: Optional[str] = None


class TaskExecutor:
    """Core task execution engine.
    
    This class manages the conversation loop, tool execution, and
    interaction with the language model.
    """
    
    def __init__(
        self,
        api_config: ApiConfiguration,
        approval_callback: Optional[Callable[[str, Dict[str, Any]], bool]] = None,
        message_callback: Optional[Callable[[str, str], None]] = None,
    ):
        """Initialize the task executor.
        
        Args:
            api_config: API configuration
            approval_callback: Optional callback for tool approval
            message_callback: Optional callback for message updates
        """
        self.api_config = api_config
        self.approval_callback = approval_callback
        self.message_callback = message_callback
        self.conversation_history: List[Dict[str, Any]] = []
        self.tools = self._initialize_tools()
    
    def _initialize_tools(self) -> Dict[str, Dict[str, Any]]:
        """Initialize available tools."""
        return {
            "execute_command": {
                "type": "function",
                "function": {
                    "name": "execute_command",
                    "description": "Execute a shell command and return the output",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "command": {
                                "type": "string",
                                "description": "The shell command to execute"
                            },
                            "cwd": {
                                "type": "string",
                                "description": "Working directory for the command (optional)"
                            }
                        },
                        "required": ["command"]
                    }
                }
            },
            "read_file": {
                "type": "function",
                "function": {
                    "name": "read_file",
                    "description": "Read the contents of a file",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "path": {
                                "type": "string",
                                "description": "Path to the file to read"
                            }
                        },
                        "required": ["path"]
                    }
                }
            },
            "write_file": {
                "type": "function",
                "function": {
                    "name": "write_file",
                    "description": "Write content to a file",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "path": {
                                "type": "string",
                                "description": "Path to the file to write"
                            },
                            "content": {
                                "type": "string",
                                "description": "Content to write to the file"
                            }
                        },
                        "required": ["path", "content"]
                    }
                }
            },
            "file_search": {
                "type": "function",
                "function": {
                    "name": "file_search",
                    "description": "Search for text patterns in files",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "pattern": {
                                "type": "string",
                                "description": "Text pattern to search for"
                            },
                            "path": {
                                "type": "string",
                                "description": "Directory path to search in (default: current directory)"
                            },
                            "file_extensions": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "File extensions to include in search"
                            }
                        },
                        "required": ["pattern"]
                    }
                }
            },
            "list_files": {
                "type": "function",
                "function": {
                    "name": "list_files",
                    "description": "List files and directories in a path",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "path": {
                                "type": "string",
                                "description": "Directory path to list (default: current directory)"
                            },
                            "recursive": {
                                "type": "boolean",
                                "description": "Whether to list files recursively"
                            }
                        }
                    }
                }
            }
        }
    
    async def execute_task(self, user_message: str, system_prompt: Optional[str] = None) -> str:
        """Execute a task with the given user message.
        
        Args:
            user_message: User's task description
            system_prompt: Optional system prompt
            
        Returns:
            Final assistant response
        """
        # Add user message to conversation
        self.conversation_history.append({
            "role": "user",
            "content": user_message
        })
        
        if self.message_callback:
            self.message_callback("user", user_message)
        
        # Start conversation loop
        max_iterations = 20  # Prevent infinite loops
        iteration = 0
        
        while iteration < max_iterations:
            iteration += 1
            
            # Prepare messages for API call
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.extend(self.conversation_history)
            
            # Call the language model
            async with create_api_handler(self.api_config) as api_handler:
                response = await api_handler.create_completion(
                    messages=messages,
                    tools=list(self.tools.values()),
                    stream=False
                )
            
            # Parse the response
            assistant_message = self._parse_response(response)
            
            # Add assistant message to conversation
            self.conversation_history.append(assistant_message)
            
            if self.message_callback:
                self.message_callback("assistant", assistant_message.get("content", ""))
            
            # Check if there are tool calls
            tool_calls = assistant_message.get("tool_calls", [])
            if not tool_calls:
                # No more tool calls, task is complete
                return assistant_message.get("content", "")
            
            # Execute tool calls
            tool_results = []
            for tool_call in tool_calls:
                result = await self._execute_tool_call(tool_call)
                tool_results.append(result)
            
            # Add tool results to conversation
            for result in tool_results:
                self.conversation_history.append({
                    "role": "tool",
                    "tool_call_id": result.tool_call_id,
                    "content": result.content
                })
        
        # If we reach here, we hit the iteration limit
        return "Task execution reached maximum iterations. The task may be incomplete."
    
    def _parse_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Parse API response into a message format.
        
        Args:
            response: Raw API response
            
        Returns:
            Parsed message dictionary
        """
        if "choices" in response:  # OpenAI format
            choice = response["choices"][0]
            message = choice["message"]
            return message
        elif "content" in response:  # Anthropic format
            content = response["content"][0]
            return {
                "role": "assistant",
                "content": content.get("text", "")
            }
        else:
            return {
                "role": "assistant",
                "content": "Error: Unable to parse response"
            }
    
    async def _execute_tool_call(self, tool_call: Dict[str, Any]) -> ToolResult:
        """Execute a single tool call.
        
        Args:
            tool_call: Tool call information
            
        Returns:
            Tool execution result
        """
        tool_name = tool_call.get("function", {}).get("name", "")
        tool_args = tool_call.get("function", {}).get("arguments", {})
        tool_id = tool_call.get("id")
        
        # Parse arguments if they're a string
        if isinstance(tool_args, str):
            try:
                tool_args = json.loads(tool_args)
            except json.JSONDecodeError:
                return ToolResult(
                    tool_call_id=tool_id,
                    content="Error: Invalid tool arguments",
                    success=False,
                    error="Invalid JSON in tool arguments"
                )
        
        # Check for approval if callback is provided
        if self.approval_callback:
            approved = self.approval_callback(tool_name, tool_args)
            if not approved:
                return ToolResult(
                    tool_call_id=tool_id,
                    content="Tool execution was not approved by user",
                    success=False,
                    error="User denied approval"
                )
        
        # Execute the tool
        try:
            if tool_name == "execute_command":
                result = execute_command(
                    tool_args["command"],
                    tool_args.get("cwd")
                )
            elif tool_name == "read_file":
                result = read_file(tool_args["path"])
            elif tool_name == "write_file":
                result = write_file(tool_args["path"], tool_args["content"])
            elif tool_name == "file_search":
                result = file_search(
                    tool_args["pattern"],
                    tool_args.get("path", "."),
                    tool_args.get("file_extensions")
                )
            elif tool_name == "list_files":
                result = list_files(
                    tool_args.get("path", "."),
                    tool_args.get("recursive", False)
                )
            else:
                result = f"Error: Unknown tool '{tool_name}'"
            
            return ToolResult(
                tool_call_id=tool_id,
                content=str(result),
                success=True
            )
            
        except Exception as e:
            return ToolResult(
                tool_call_id=tool_id,
                content=f"Error executing {tool_name}: {str(e)}",
                success=False,
                error=str(e)
            )
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """Get the current conversation history.
        
        Returns:
            List of conversation messages
        """
        return self.conversation_history.copy()
    
    def clear_conversation(self) -> None:
        """Clear the conversation history."""
        self.conversation_history.clear()
