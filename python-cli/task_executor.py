"""Task execution engine for Cline CLI.

This module provides the core task execution functionality, including
tool execution, message parsing, and conversation management.
"""
import asyncio
import json
import re
import time
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass
from enum import Enum

from api import ApiConfiguration, create_api_handler
from tools import (
    execute_command,
    read_file,
    write_file,
    file_search,
    list_files,
    regex_search_files,
    extract_text_from_file,
    get_folder_size,
)
from browser_session import BrowserSession, BrowserSettings, BrowserActionResult
from mcp_hub import McpHub, McpServer
from code_parser import parse_source_code_for_definitions_top_level, get_code_definitions_summary
from url_content_fetcher import fetch_url_content, UrlContent
from checkpoint_tracker import CheckpointTracker
from diff_viewer import TerminalDiffViewer, show_file_diff, show_string_diff, show_git_diff


class ToolType(Enum):
    """Available tool types."""
    EXECUTE_COMMAND = "execute_command"
    READ_FILE = "read_file"
    WRITE_FILE = "write_file"
    FILE_SEARCH = "file_search"
    LIST_FILES = "list_files"
    REGEX_SEARCH = "regex_search_files"
    EXTRACT_TEXT = "extract_text_from_file"
    GET_FOLDER_SIZE = "get_folder_size"
    BROWSER_ACTION = "browser_action"
    FETCH_URL = "fetch_url_content"
    PARSE_CODE = "parse_source_code"
    CODE_DEFINITIONS = "get_code_definitions"
    MCP_CALL_TOOL = "mcp_call_tool"
    DIFF_FILES = "diff_files"
    DIFF_STRINGS = "diff_strings"
    GIT_DIFF = "git_diff"


@dataclass
class ToolCall:
    """Represents a tool call from the assistant."""
    name: str
    arguments: Dict[str, Any]
    id: Optional[str] = None


@dataclass
class ToolResult:
    """Represents the result of a tool execution."""
    tool_call_id: Optional[str]
    content: str
    success: bool = True
    error: Optional[str] = None


class TaskExecutor:
    """Core task execution engine.

    This class manages the conversation loop, tool execution, and
    interaction with the language model.
    """

    def __init__(
        self,
        api_config: ApiConfiguration,
        approval_callback: Optional[Callable[[str, Dict[str, Any]], bool]] = None,
        message_callback: Optional[Callable[[str, str], None]] = None,
        workspace_path: Optional[str] = None,
        enable_browser: bool = False,
        enable_mcp: bool = False,
        enable_checkpoints: bool = True,
    ):
        """Initialize the task executor.

        Args:
            api_config: API configuration
            approval_callback: Optional callback for tool approval
            message_callback: Optional callback for message updates
            workspace_path: Path to the workspace directory
            enable_browser: Whether to enable browser automation
            enable_mcp: Whether to enable MCP support
            enable_checkpoints: Whether to enable checkpoint system
        """
        self.api_config = api_config
        self.approval_callback = approval_callback
        self.message_callback = message_callback
        self.workspace_path = workspace_path
        self.conversation_history: List[Dict[str, Any]] = []
        self.tools = self._initialize_tools()

        # Initialize optional components
        self.browser_session = None
        self.mcp_hub = None
        self.checkpoint_tracker = None

        if enable_browser:
            try:
                browser_settings = BrowserSettings()
                self.browser_session = BrowserSession(browser_settings)
            except ImportError:
                print("Warning: Browser automation disabled. Install playwright: pip install playwright")

        if enable_mcp:
            self.mcp_hub = McpHub()

        if enable_checkpoints:
            self.checkpoint_tracker = CheckpointTracker()

    def _initialize_tools(self) -> Dict[str, Dict[str, Any]]:
        """Initialize available tools."""
        return {
            "execute_command": {
                "type": "function",
                "function": {
                    "name": "execute_command",
                    "description": "Execute a shell command and return the output",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "command": {
                                "type": "string",
                                "description": "The shell command to execute"
                            },
                            "cwd": {
                                "type": "string",
                                "description": "Working directory for the command (optional)"
                            }
                        },
                        "required": ["command"]
                    }
                }
            },
            "read_file": {
                "type": "function",
                "function": {
                    "name": "read_file",
                    "description": "Read the contents of a file",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "path": {
                                "type": "string",
                                "description": "Path to the file to read"
                            }
                        },
                        "required": ["path"]
                    }
                }
            },
            "write_file": {
                "type": "function",
                "function": {
                    "name": "write_file",
                    "description": "Write content to a file",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "path": {
                                "type": "string",
                                "description": "Path to the file to write"
                            },
                            "content": {
                                "type": "string",
                                "description": "Content to write to the file"
                            }
                        },
                        "required": ["path", "content"]
                    }
                }
            },
            "file_search": {
                "type": "function",
                "function": {
                    "name": "file_search",
                    "description": "Search for text patterns in files",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "pattern": {
                                "type": "string",
                                "description": "Text pattern to search for"
                            },
                            "path": {
                                "type": "string",
                                "description": "Directory path to search in (default: current directory)"
                            },
                            "file_extensions": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "File extensions to include in search"
                            }
                        },
                        "required": ["pattern"]
                    }
                }
            },
            "list_files": {
                "type": "function",
                "function": {
                    "name": "list_files",
                    "description": "List files and directories in a path",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "path": {
                                "type": "string",
                                "description": "Directory path to list (default: current directory)"
                            },
                            "recursive": {
                                "type": "boolean",
                                "description": "Whether to list files recursively"
                            }
                        }
                    }
                }
            },
            "regex_search_files": {
                "type": "function",
                "function": {
                    "name": "regex_search_files",
                    "description": "Advanced regex search in files with context",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "pattern": {
                                "type": "string",
                                "description": "Regular expression pattern to search for"
                            },
                            "path": {
                                "type": "string",
                                "description": "Directory path to search in (default: current directory)"
                            },
                            "file_extensions": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "File extensions to include in search"
                            },
                            "case_sensitive": {
                                "type": "boolean",
                                "description": "Whether search should be case sensitive"
                            },
                            "context_lines": {
                                "type": "integer",
                                "description": "Number of context lines to show around matches"
                            }
                        },
                        "required": ["pattern"]
                    }
                }
            },
            "extract_text_from_file": {
                "type": "function",
                "function": {
                    "name": "extract_text_from_file",
                    "description": "Extract text content from various file types (PDF, DOCX, etc.)",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "path": {
                                "type": "string",
                                "description": "Path to the file to extract text from"
                            }
                        },
                        "required": ["path"]
                    }
                }
            },
            "get_folder_size": {
                "type": "function",
                "function": {
                    "name": "get_folder_size",
                    "description": "Get the total size and file count of a directory",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "path": {
                                "type": "string",
                                "description": "Path to the directory"
                            }
                        },
                        "required": ["path"]
                    }
                }
            },
            "browser_action": {
                "type": "function",
                "function": {
                    "name": "browser_action",
                    "description": "Perform browser automation actions (launch, navigate, click, type, scroll, close)",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "action": {
                                "type": "string",
                                "enum": ["launch", "navigate", "click", "type", "scroll_down", "scroll_up", "close"],
                                "description": "Browser action to perform"
                            },
                            "url": {
                                "type": "string",
                                "description": "URL to navigate to (for navigate action)"
                            },
                            "coordinate": {
                                "type": "string",
                                "description": "Coordinate to click (format: 'x,y')"
                            },
                            "text": {
                                "type": "string",
                                "description": "Text to type"
                            }
                        },
                        "required": ["action"]
                    }
                }
            },
            "fetch_url_content": {
                "type": "function",
                "function": {
                    "name": "fetch_url_content",
                    "description": "Fetch and extract content from a URL",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "url": {
                                "type": "string",
                                "description": "URL to fetch content from"
                            }
                        },
                        "required": ["url"]
                    }
                }
            },
            "parse_source_code": {
                "type": "function",
                "function": {
                    "name": "parse_source_code",
                    "description": "Parse source code and extract definitions (functions, classes, etc.)",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "path": {
                                "type": "string",
                                "description": "Path to the source file to parse"
                            }
                        },
                        "required": ["path"]
                    }
                }
            },
            "get_code_definitions": {
                "type": "function",
                "function": {
                    "name": "get_code_definitions",
                    "description": "Get a summary of code definitions in a directory",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "path": {
                                "type": "string",
                                "description": "Path to the directory to analyze"
                            }
                        },
                        "required": ["path"]
                    }
                }
            },
            "diff_files": {
                "type": "function",
                "function": {
                    "name": "diff_files",
                    "description": "Compare two files and show visual diff",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "file1_path": {
                                "type": "string",
                                "description": "Path to the first file (old version)"
                            },
                            "file2_path": {
                                "type": "string",
                                "description": "Path to the second file (new version)"
                            },
                            "context_lines": {
                                "type": "integer",
                                "description": "Number of context lines to show around changes (default: 3)"
                            },
                            "side_by_side": {
                                "type": "boolean",
                                "description": "Whether to show side-by-side diff view"
                            }
                        },
                        "required": ["file1_path", "file2_path"]
                    }
                }
            },
            "diff_strings": {
                "type": "function",
                "function": {
                    "name": "diff_strings",
                    "description": "Compare two strings and show visual diff",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "old_content": {
                                "type": "string",
                                "description": "Original content"
                            },
                            "new_content": {
                                "type": "string",
                                "description": "New content"
                            },
                            "file_path": {
                                "type": "string",
                                "description": "Virtual file path for display (default: 'content')"
                            },
                            "side_by_side": {
                                "type": "boolean",
                                "description": "Whether to show side-by-side diff view"
                            }
                        },
                        "required": ["old_content", "new_content"]
                    }
                }
            },
            "git_diff": {
                "type": "function",
                "function": {
                    "name": "git_diff",
                    "description": "Show git diff for a file with visual formatting",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "file_path": {
                                "type": "string",
                                "description": "Path to the file to show diff for"
                            },
                            "staged": {
                                "type": "boolean",
                                "description": "Whether to show staged changes (default: false)"
                            }
                        },
                        "required": ["file_path"]
                    }
                }
            }
        }

    async def execute_task(self, user_message: str, system_prompt: Optional[str] = None) -> str:
        """Execute a task with the given user message.

        Args:
            user_message: User's task description
            system_prompt: Optional system prompt

        Returns:
            Final assistant response
        """
        # Add user message to conversation
        self.conversation_history.append({
            "role": "user",
            "content": user_message
        })

        if self.message_callback:
            self.message_callback("user", user_message)

        # Start conversation loop
        max_iterations = 20  # Prevent infinite loops
        iteration = 0

        while iteration < max_iterations:
            iteration += 1

            # Prepare messages for API call
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.extend(self.conversation_history)

            # Call the language model
            async with create_api_handler(self.api_config) as api_handler:
                response = await api_handler.create_completion(
                    messages=messages,
                    tools=list(self.tools.values()),
                    stream=False
                )

            # Parse the response
            assistant_message = self._parse_response(response)

            # Add assistant message to conversation
            self.conversation_history.append(assistant_message)

            if self.message_callback:
                self.message_callback("assistant", assistant_message.get("content", ""))

            # Check if there are tool calls
            tool_calls = assistant_message.get("tool_calls", [])
            if not tool_calls:
                # No more tool calls, task is complete
                return assistant_message.get("content", "")

            # Execute tool calls
            tool_results = []
            for tool_call in tool_calls:
                result = await self._execute_tool_call(tool_call)
                tool_results.append(result)

            # Add tool results to conversation
            for result in tool_results:
                self.conversation_history.append({
                    "role": "tool",
                    "tool_call_id": result.tool_call_id,
                    "content": result.content
                })

        # If we reach here, we hit the iteration limit
        return "Task execution reached maximum iterations. The task may be incomplete."

    def _parse_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Parse API response into a message format.

        Args:
            response: Raw API response

        Returns:
            Parsed message dictionary
        """
        if "choices" in response:  # OpenAI format
            choice = response["choices"][0]
            message = choice["message"]
            return message
        elif "content" in response:  # Anthropic format
            content = response["content"][0]
            return {
                "role": "assistant",
                "content": content.get("text", "")
            }
        else:
            return {
                "role": "assistant",
                "content": "Error: Unable to parse response"
            }

    async def _execute_tool_call(self, tool_call: Dict[str, Any]) -> ToolResult:
        """Execute a single tool call.

        Args:
            tool_call: Tool call information

        Returns:
            Tool execution result
        """
        tool_name = tool_call.get("function", {}).get("name", "")
        tool_args = tool_call.get("function", {}).get("arguments", {})
        tool_id = tool_call.get("id")

        # Parse arguments if they're a string
        if isinstance(tool_args, str):
            try:
                tool_args = json.loads(tool_args)
            except json.JSONDecodeError:
                return ToolResult(
                    tool_call_id=tool_id,
                    content="Error: Invalid tool arguments",
                    success=False,
                    error="Invalid JSON in tool arguments"
                )

        # Check for approval if callback is provided
        if self.approval_callback:
            approved = self.approval_callback(tool_name, tool_args)
            if not approved:
                return ToolResult(
                    tool_call_id=tool_id,
                    content="Tool execution was not approved by user",
                    success=False,
                    error="User denied approval"
                )

        # Execute the tool
        try:
            if tool_name == "execute_command":
                result = execute_command(
                    tool_args["command"],
                    tool_args.get("cwd")
                )
            elif tool_name == "read_file":
                result = read_file(tool_args["path"])
            elif tool_name == "write_file":
                result = write_file(tool_args["path"], tool_args["content"])
            elif tool_name == "file_search":
                result = file_search(
                    tool_args["pattern"],
                    tool_args.get("path", "."),
                    tool_args.get("file_extensions")
                )
            elif tool_name == "list_files":
                result = list_files(
                    tool_args.get("path", "."),
                    tool_args.get("recursive", False)
                )
            elif tool_name == "regex_search_files":
                result = regex_search_files(
                    tool_args["pattern"],
                    tool_args.get("path", "."),
                    tool_args.get("file_extensions"),
                    tool_args.get("case_sensitive", False),
                    tool_args.get("max_results", 100),
                    tool_args.get("context_lines", 2)
                )
            elif tool_name == "extract_text_from_file":
                result = extract_text_from_file(tool_args["path"])
            elif tool_name == "get_folder_size":
                result = get_folder_size(tool_args["path"])
            elif tool_name == "browser_action":
                result = await self._execute_browser_action(tool_args)
            elif tool_name == "fetch_url_content":
                result = await self._execute_fetch_url(tool_args["url"])
            elif tool_name == "parse_source_code":
                result = self._execute_parse_code(tool_args["path"])
            elif tool_name == "get_code_definitions":
                result = get_code_definitions_summary(tool_args["path"])
            elif tool_name == "mcp_call_tool":
                result = await self._execute_mcp_tool(tool_args)
            elif tool_name == "diff_files":
                result = self._execute_diff_files(tool_args)
            elif tool_name == "diff_strings":
                result = self._execute_diff_strings(tool_args)
            elif tool_name == "git_diff":
                result = self._execute_git_diff(tool_args)
            else:
                result = f"Error: Unknown tool '{tool_name}'"

            return ToolResult(
                tool_call_id=tool_id,
                content=str(result),
                success=True
            )

        except Exception as e:
            return ToolResult(
                tool_call_id=tool_id,
                content=f"Error executing {tool_name}: {str(e)}",
                success=False,
                error=str(e)
            )

    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """Get the current conversation history.

        Returns:
            List of conversation messages
        """
        return self.conversation_history.copy()

    def clear_conversation(self) -> None:
        """Clear the conversation history."""
        self.conversation_history.clear()

    async def _execute_browser_action(self, tool_args: Dict[str, Any]) -> str:
        """Execute a browser action.

        Args:
            tool_args: Tool arguments

        Returns:
            Action result as string
        """
        if not self.browser_session:
            return "Error: Browser automation not enabled. Initialize with enable_browser=True"

        action = tool_args.get("action")

        try:
            if action == "launch":
                result = await self.browser_session.launch_browser()
            elif action == "navigate":
                url = tool_args.get("url")
                if not url:
                    return "Error: URL required for navigate action"
                result = await self.browser_session.navigate_to_url(url)
            elif action == "click":
                coordinate = tool_args.get("coordinate")
                if not coordinate:
                    return "Error: Coordinate required for click action"
                result = await self.browser_session.click(coordinate)
            elif action == "type":
                text = tool_args.get("text")
                if not text:
                    return "Error: Text required for type action"
                result = await self.browser_session.type_text(text)
            elif action == "scroll_down":
                result = await self.browser_session.scroll_down()
            elif action == "scroll_up":
                result = await self.browser_session.scroll_up()
            elif action == "close":
                result = await self.browser_session.close_browser()
            else:
                return f"Error: Unknown browser action '{action}'"

            if result.success:
                response = f"Browser action '{action}' completed successfully"
                if result.current_url:
                    response += f"\nCurrent URL: {result.current_url}"
                if result.logs:
                    response += f"\nLogs: {result.logs}"
                if result.screenshot:
                    response += f"\nScreenshot captured (base64 length: {len(result.screenshot)})"
                return response
            else:
                return f"Browser action failed: {result.error}"

        except Exception as e:
            return f"Error executing browser action: {str(e)}"

    async def _execute_fetch_url(self, url: str) -> str:
        """Execute URL content fetching.

        Args:
            url: URL to fetch

        Returns:
            Fetched content as string
        """
        try:
            content = await fetch_url_content(url)

            if content.error:
                return f"Error fetching URL: {content.error}"

            response = f"URL: {content.url}\n"
            if content.title:
                response += f"Title: {content.title}\n"
            if content.content_type:
                response += f"Content-Type: {content.content_type}\n"
            response += f"\nContent:\n{content.content}"

            return response

        except Exception as e:
            return f"Error fetching URL content: {str(e)}"

    def _execute_parse_code(self, file_path: str) -> str:
        """Execute code parsing.

        Args:
            file_path: Path to the source file

        Returns:
            Parsed definitions as string
        """
        try:
            definitions = parse_source_code_for_definitions_top_level(file_path)

            if not definitions:
                return f"No code definitions found in '{file_path}'"

            response = f"Code definitions in '{file_path}':\n\n"

            for defn in definitions:
                response += f"- {defn['type'].title()}: {defn['name']}"
                if defn.get('parent'):
                    response += f" (in {defn['parent']})"
                response += f" (line {defn['line_start']})\n"

                if defn.get('signature'):
                    response += f"  Signature: {defn['signature']}\n"

                if defn.get('docstring'):
                    # Truncate long docstrings
                    docstring = defn['docstring']
                    if len(docstring) > 200:
                        docstring = docstring[:200] + "..."
                    response += f"  Doc: {docstring}\n"

                response += "\n"

            return response

        except Exception as e:
            return f"Error parsing code: {str(e)}"

    async def _execute_mcp_tool(self, tool_args: Dict[str, Any]) -> str:
        """Execute an MCP tool call.

        Args:
            tool_args: Tool arguments

        Returns:
            Tool result as string
        """
        if not self.mcp_hub:
            return "Error: MCP support not enabled. Initialize with enable_mcp=True"

        tool_name = tool_args.get("tool_name")
        arguments = tool_args.get("arguments", {})

        if not tool_name:
            return "Error: tool_name required for MCP tool call"

        try:
            result = await self.mcp_hub.call_tool(tool_name, arguments)

            if "error" in result:
                return f"MCP tool error: {result['error']}"

            return f"MCP tool '{tool_name}' result: {json.dumps(result, indent=2)}"

        except Exception as e:
            return f"Error executing MCP tool: {str(e)}"

    async def create_checkpoint(self, description: str = "Manual checkpoint") -> Optional[str]:
        """Create a checkpoint of the current task state.

        Args:
            description: Description of the checkpoint

        Returns:
            Checkpoint ID if created successfully
        """
        if not self.checkpoint_tracker:
            return None

        task_id = f"task_{int(time.time())}"

        try:
            checkpoint_id = self.checkpoint_tracker.create_checkpoint(
                task_id=task_id,
                description=description,
                conversation_history=self.conversation_history,
                workspace_path=self.workspace_path
            )
            return checkpoint_id

        except Exception as e:
            print(f"Error creating checkpoint: {e}")
            return None

    async def restore_checkpoint(self, checkpoint_id: str) -> bool:
        """Restore from a checkpoint.

        Args:
            checkpoint_id: ID of the checkpoint to restore

        Returns:
            True if restored successfully
        """
        if not self.checkpoint_tracker:
            return False

        try:
            checkpoint = self.checkpoint_tracker.restore_checkpoint(
                checkpoint_id,
                self.workspace_path
            )

            if checkpoint:
                self.conversation_history = checkpoint.conversation_history.copy()
                return True

            return False

        except Exception as e:
            print(f"Error restoring checkpoint: {e}")
            return False

    def _execute_diff_files(self, tool_args: Dict[str, Any]) -> str:
        """Execute file diff comparison.

        Args:
            tool_args: Tool arguments

        Returns:
            Diff result as string
        """
        try:
            file1_path = tool_args["file1_path"]
            file2_path = tool_args["file2_path"]
            context_lines = tool_args.get("context_lines", 3)
            side_by_side = tool_args.get("side_by_side", False)

            viewer = TerminalDiffViewer(context_lines=context_lines)
            file_diff = viewer.compare_files(file1_path, file2_path)

            if side_by_side:
                return viewer.create_side_by_side_diff(file_diff)
            else:
                return viewer.display_diff(file_diff)

        except Exception as e:
            return f"Error comparing files: {str(e)}"

    def _execute_diff_strings(self, tool_args: Dict[str, Any]) -> str:
        """Execute string diff comparison.

        Args:
            tool_args: Tool arguments

        Returns:
            Diff result as string
        """
        try:
            old_content = tool_args["old_content"]
            new_content = tool_args["new_content"]
            file_path = tool_args.get("file_path", "content")
            side_by_side = tool_args.get("side_by_side", False)

            viewer = TerminalDiffViewer()
            file_diff = viewer.compare_strings(old_content, new_content, file_path)

            if side_by_side:
                return viewer.create_side_by_side_diff(file_diff)
            else:
                return viewer.display_diff(file_diff)

        except Exception as e:
            return f"Error comparing strings: {str(e)}"

    def _execute_git_diff(self, tool_args: Dict[str, Any]) -> str:
        """Execute git diff.

        Args:
            tool_args: Tool arguments

        Returns:
            Git diff result as string
        """
        try:
            file_path = tool_args["file_path"]
            staged = tool_args.get("staged", False)

            return show_git_diff(file_path, staged)

        except Exception as e:
            return f"Error getting git diff: {str(e)}"
