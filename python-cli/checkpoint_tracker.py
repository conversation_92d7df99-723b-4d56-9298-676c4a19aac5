"""Checkpoint tracking system for Cline CLI.

This module provides checkpoint functionality to save and restore task states,
similar to the CheckpointTracker in the JavaScript version.
"""
import json
import time
import shutil
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
import logging


@dataclass
class Checkpoint:
    """Represents a task checkpoint."""
    id: str
    task_id: str
    timestamp: float
    description: str
    conversation_history: List[Dict[str, Any]]
    file_states: Dict[str, str]  # file_path -> content_hash
    metadata: Dict[str, Any]


@dataclass
class FileState:
    """Represents the state of a file at checkpoint time."""
    path: str
    content: str
    hash: str
    size: int
    modified_time: float


class CheckpointTracker:
    """Tracks and manages task checkpoints."""
    
    def __init__(self, checkpoints_dir: Optional[str] = None):
        """Initialize the checkpoint tracker.
        
        Args:
            checkpoints_dir: Directory to store checkpoints
        """
        self.checkpoints_dir = Path(checkpoints_dir or "checkpoints")
        self.checkpoints_dir.mkdir(exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
        self.max_checkpoints_per_task = 10
        self.auto_checkpoint_interval = 300  # 5 minutes
        self.last_auto_checkpoint = 0
    
    def create_checkpoint(
        self,
        task_id: str,
        description: str,
        conversation_history: List[Dict[str, Any]],
        workspace_path: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a new checkpoint.
        
        Args:
            task_id: ID of the task
            description: Description of the checkpoint
            conversation_history: Current conversation history
            workspace_path: Path to the workspace to snapshot
            metadata: Additional metadata
            
        Returns:
            Checkpoint ID
        """
        checkpoint_id = f"{task_id}_{int(time.time())}"
        timestamp = time.time()
        
        # Capture file states
        file_states = {}
        if workspace_path:
            file_states = self._capture_file_states(workspace_path)
        
        # Create checkpoint
        checkpoint = Checkpoint(
            id=checkpoint_id,
            task_id=task_id,
            timestamp=timestamp,
            description=description,
            conversation_history=conversation_history.copy(),
            file_states=file_states,
            metadata=metadata or {}
        )
        
        # Save checkpoint
        self._save_checkpoint(checkpoint)
        
        # Clean up old checkpoints
        self._cleanup_old_checkpoints(task_id)
        
        self.logger.info(f"Created checkpoint {checkpoint_id} for task {task_id}")
        return checkpoint_id
    
    def restore_checkpoint(self, checkpoint_id: str, workspace_path: Optional[str] = None) -> Optional[Checkpoint]:
        """Restore a checkpoint.
        
        Args:
            checkpoint_id: ID of the checkpoint to restore
            workspace_path: Path to restore files to
            
        Returns:
            Restored checkpoint or None if not found
        """
        checkpoint = self._load_checkpoint(checkpoint_id)
        if not checkpoint:
            return None
        
        # Restore file states if workspace path provided
        if workspace_path and checkpoint.file_states:
            self._restore_file_states(checkpoint, workspace_path)
        
        self.logger.info(f"Restored checkpoint {checkpoint_id}")
        return checkpoint
    
    def list_checkpoints(self, task_id: Optional[str] = None) -> List[Checkpoint]:
        """List available checkpoints.
        
        Args:
            task_id: Optional task ID to filter by
            
        Returns:
            List of checkpoints
        """
        checkpoints = []
        
        for checkpoint_file in self.checkpoints_dir.glob("*.json"):
            try:
                checkpoint = self._load_checkpoint_from_file(checkpoint_file)
                if checkpoint and (not task_id or checkpoint.task_id == task_id):
                    checkpoints.append(checkpoint)
            except Exception as e:
                self.logger.warning(f"Failed to load checkpoint {checkpoint_file}: {e}")
        
        # Sort by timestamp (newest first)
        checkpoints.sort(key=lambda c: c.timestamp, reverse=True)
        return checkpoints
    
    def delete_checkpoint(self, checkpoint_id: str) -> bool:
        """Delete a checkpoint.
        
        Args:
            checkpoint_id: ID of the checkpoint to delete
            
        Returns:
            True if deleted successfully
        """
        checkpoint_file = self.checkpoints_dir / f"{checkpoint_id}.json"
        backup_dir = self.checkpoints_dir / f"{checkpoint_id}_files"
        
        try:
            if checkpoint_file.exists():
                checkpoint_file.unlink()
            
            if backup_dir.exists():
                shutil.rmtree(backup_dir)
            
            self.logger.info(f"Deleted checkpoint {checkpoint_id}")
            return True
        
        except Exception as e:
            self.logger.error(f"Failed to delete checkpoint {checkpoint_id}: {e}")
            return False
    
    def should_auto_checkpoint(self) -> bool:
        """Check if an automatic checkpoint should be created.
        
        Returns:
            True if auto checkpoint is due
        """
        current_time = time.time()
        return (current_time - self.last_auto_checkpoint) >= self.auto_checkpoint_interval
    
    def create_auto_checkpoint(
        self,
        task_id: str,
        conversation_history: List[Dict[str, Any]],
        workspace_path: Optional[str] = None
    ) -> Optional[str]:
        """Create an automatic checkpoint if due.
        
        Args:
            task_id: ID of the task
            conversation_history: Current conversation history
            workspace_path: Path to the workspace
            
        Returns:
            Checkpoint ID if created, None otherwise
        """
        if not self.should_auto_checkpoint():
            return None
        
        checkpoint_id = self.create_checkpoint(
            task_id=task_id,
            description="Auto checkpoint",
            conversation_history=conversation_history,
            workspace_path=workspace_path,
            metadata={"auto": True}
        )
        
        self.last_auto_checkpoint = time.time()
        return checkpoint_id
    
    def _capture_file_states(self, workspace_path: str) -> Dict[str, str]:
        """Capture the current state of files in the workspace.
        
        Args:
            workspace_path: Path to the workspace
            
        Returns:
            Dictionary mapping file paths to content hashes
        """
        import hashlib
        
        file_states = {}
        workspace = Path(workspace_path)
        
        if not workspace.exists():
            return file_states
        
        # Get all text files
        text_extensions = {'.py', '.js', '.ts', '.txt', '.md', '.json', '.yaml', '.yml', '.html', '.css', '.sh'}
        
        for file_path in workspace.rglob('*'):
            if (file_path.is_file() and 
                file_path.suffix.lower() in text_extensions and
                not any(part.startswith('.') for part in file_path.parts)):
                
                try:
                    content = file_path.read_text(encoding='utf-8', errors='ignore')
                    content_hash = hashlib.md5(content.encode()).hexdigest()
                    
                    relative_path = str(file_path.relative_to(workspace))
                    file_states[relative_path] = content_hash
                    
                    # Save file content for restoration
                    self._backup_file(file_path, relative_path)
                    
                except Exception as e:
                    self.logger.warning(f"Failed to capture state of {file_path}: {e}")
        
        return file_states
    
    def _backup_file(self, file_path: Path, relative_path: str) -> None:
        """Backup a file for checkpoint restoration.
        
        Args:
            file_path: Absolute path to the file
            relative_path: Relative path for backup storage
        """
        # This is a simplified backup - in practice you might want more sophisticated versioning
        backup_dir = self.checkpoints_dir / "file_backups"
        backup_dir.mkdir(exist_ok=True)
        
        backup_file = backup_dir / f"{relative_path.replace('/', '_')}"
        try:
            shutil.copy2(file_path, backup_file)
        except Exception as e:
            self.logger.warning(f"Failed to backup {file_path}: {e}")
    
    def _restore_file_states(self, checkpoint: Checkpoint, workspace_path: str) -> None:
        """Restore file states from a checkpoint.
        
        Args:
            checkpoint: Checkpoint to restore from
            workspace_path: Path to restore files to
        """
        workspace = Path(workspace_path)
        backup_dir = self.checkpoints_dir / "file_backups"
        
        for relative_path, expected_hash in checkpoint.file_states.items():
            file_path = workspace / relative_path
            backup_file = backup_dir / f"{relative_path.replace('/', '_')}"
            
            if backup_file.exists():
                try:
                    # Create directory if needed
                    file_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # Restore file
                    shutil.copy2(backup_file, file_path)
                    
                    self.logger.debug(f"Restored {relative_path}")
                    
                except Exception as e:
                    self.logger.warning(f"Failed to restore {relative_path}: {e}")
    
    def _save_checkpoint(self, checkpoint: Checkpoint) -> None:
        """Save a checkpoint to disk.
        
        Args:
            checkpoint: Checkpoint to save
        """
        checkpoint_file = self.checkpoints_dir / f"{checkpoint.id}.json"
        
        try:
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(checkpoint), f, indent=2, ensure_ascii=False)
        
        except Exception as e:
            self.logger.error(f"Failed to save checkpoint {checkpoint.id}: {e}")
            raise
    
    def _load_checkpoint(self, checkpoint_id: str) -> Optional[Checkpoint]:
        """Load a checkpoint from disk.
        
        Args:
            checkpoint_id: ID of the checkpoint to load
            
        Returns:
            Loaded checkpoint or None if not found
        """
        checkpoint_file = self.checkpoints_dir / f"{checkpoint_id}.json"
        return self._load_checkpoint_from_file(checkpoint_file)
    
    def _load_checkpoint_from_file(self, checkpoint_file: Path) -> Optional[Checkpoint]:
        """Load a checkpoint from a file.
        
        Args:
            checkpoint_file: Path to the checkpoint file
            
        Returns:
            Loaded checkpoint or None if failed
        """
        if not checkpoint_file.exists():
            return None
        
        try:
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return Checkpoint(**data)
        
        except Exception as e:
            self.logger.error(f"Failed to load checkpoint from {checkpoint_file}: {e}")
            return None
    
    def _cleanup_old_checkpoints(self, task_id: str) -> None:
        """Clean up old checkpoints for a task.
        
        Args:
            task_id: Task ID to clean up checkpoints for
        """
        checkpoints = self.list_checkpoints(task_id)
        
        if len(checkpoints) > self.max_checkpoints_per_task:
            # Keep the most recent checkpoints
            to_delete = checkpoints[self.max_checkpoints_per_task:]
            
            for checkpoint in to_delete:
                self.delete_checkpoint(checkpoint.id)
    
    def get_checkpoint_summary(self, checkpoint_id: str) -> Optional[Dict[str, Any]]:
        """Get a summary of a checkpoint.
        
        Args:
            checkpoint_id: ID of the checkpoint
            
        Returns:
            Checkpoint summary or None if not found
        """
        checkpoint = self._load_checkpoint(checkpoint_id)
        if not checkpoint:
            return None
        
        return {
            'id': checkpoint.id,
            'task_id': checkpoint.task_id,
            'timestamp': checkpoint.timestamp,
            'description': checkpoint.description,
            'message_count': len(checkpoint.conversation_history),
            'file_count': len(checkpoint.file_states),
            'metadata': checkpoint.metadata
        }
