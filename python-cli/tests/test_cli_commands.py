"""Test CLI commands using Typer's <PERSON>li<PERSON><PERSON>ner."""
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from cli import app

runner = CliRunner()


def test_cli_version():
    """Test the --version flag."""
    result = runner.invoke(app, ["--version"])
    assert result.exit_code == 0
    assert "Cline CLI Version: 0.1.0" in result.output


def test_run_command():
    """Test the run command."""
    with patch("cli.execute_command") as mock_execute:
        mock_execute.return_value = "Command output"
        result = runner.invoke(app, ["run", "echo hello"])
        
        assert result.exit_code == 0
        assert "Command output" in result.output
        mock_execute.assert_called_once_with("echo hello", cwd=None)


def test_search_command(tmp_path, monkeypatch):
    """Test the search command."""
    # Create a test file
    test_file = tmp_path / "test.txt"
    test_file.write_text("This is a test\nWith some content\nAnd more content")
    
    # Change to the temp directory
    monkeypatch.chdir(tmp_path)
    
    # Test search
    result = runner.invoke(app, ["search", "content", "--ext", "txt"])
    
    assert result.exit_code == 0
    assert "test.txt" in result.output
    assert "content" in result.output


def test_read_command(tmp_path, monkeypatch):
    """Test the read command."""
    # Create a test file
    test_file = tmp_path / "read_test.txt"
    test_content = "This is a test file\nWith multiple lines"
    test_file.write_text(test_content)
    
    result = runner.invoke(app, ["read", str(test_file)])
    
    assert result.exit_code == 0
    assert test_content in result.output


def test_write_command(tmp_path, monkeypatch):
    """Test the write command with direct content."""
    test_file = tmp_path / "write_test.txt"
    test_content = "Test content"
    
    with patch("typer.edit") as mock_edit:
        # Test with direct content
        result = runner.invoke(app, ["write", str(test_file), "--content", test_content])
        
        assert result.exit_code == 0
        assert test_file.exists()
        assert test_file.read_text() == test_content


def test_shell_command_exit():
    """Test the shell command exit behavior."""
    with patch("builtins.input", side_effect=["exit"]) as mock_input:
        result = runner.invoke(app, ["shell"], input="exit\n")
        
        assert result.exit_code == 0
        assert "Interactive Shell" in result.output
        mock_input.assert_called()


def test_shell_command_help():
    """Test the shell command help."""
    with patch("builtins.input", side_effect=["help", "exit"]) as mock_input:
        result = runner.invoke(app, ["shell"], input="help\nexit\n")
        
        assert result.exit_code == 0
        assert "Available commands:" in result.output


def test_shell_command_run():
    """Test running commands in shell mode."""
    with patch("cli.execute_command") as mock_execute, \
         patch("builtins.input", side_effect=["run echo hello", "exit"]):
        mock_execute.return_value = "hello\n"
        result = runner.invoke(app, ["shell"], input="run echo hello\nexit\n")
        
        assert result.exit_code == 0
        mock_execute.assert_called_once_with("echo hello", cwd=None)


def test_shell_command_search(tmp_path, monkeypatch):
    """Test search in shell mode."""
    # Create a test file
    test_file = tmp_path / "shell_search.txt"
    test_file.write_text("Test content for search")
    monkeypatch.chdir(tmp_path)
    
    with patch("builtins.input", side_effect=[f"search content --ext txt", "exit"]):
        result = runner.invoke(app, ["shell"], input="search content --ext txt\nexit\n")
        
        assert result.exit_code == 0
        assert "shell_search.txt" in result.output


def test_invalid_command():
    """Test invalid command handling."""
    result = runner.invoke(app, ["invalid-command"])
    assert result.exit_code != 0
    assert "No such command" in result.output or "Error" in result.output


def test_run_command_error():
    """Test error handling in run command."""
    with patch("cli.execute_command") as mock_execute:
        mock_execute.return_value = "Error: Command failed"
        result = runner.invoke(app, ["run", "nonexistent-command"])
        
        assert result.exit_code == 0  # We don't fail on command errors
        assert "Error:" in result.output
