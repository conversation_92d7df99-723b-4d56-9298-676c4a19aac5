"""Tests for the Context class."""
import json
from pathlib import Path
from unittest.mock import mock_open, patch

import pytest

from context import Context


def test_context_initialization(tmp_path):
    """Test Context initialization."""
    config_path = str(tmp_path / "config.json")
    context = Context(config_path=config_path)
    
    assert context.config == {}
    assert context.history == []
    assert context.config_path == config_path


def test_load_config_existing_file(tmp_path):
    """Test loading config from an existing file."""
    config_path = str(tmp_path / "config.json")
    config_data = {"key": "value"}
    
    with open(config_path, 'w') as f:
        json.dump(config_data, f)
    
    context = Context(config_path=config_path)
    assert context.config == config_data


def test_load_config_invalid_json(tmp_path):
    """Test loading config from an invalid JSON file."""
    config_path = str(tmp_path / "config.json")
    
    with open(config_path, 'w') as f:
        f.write("{invalid json}")
    
    context = Context(config_path=config_path)
    assert context.config == {}


def test_save_config(tmp_path):
    """Test saving config to file."""
    config_path = str(tmp_path / "config.json")
    context = Context(config_path=config_path)
    
    context.config = {"key": "value"}
    context.save_config()
    
    with open(config_path, 'r') as f:
        saved_data = json.load(f)
    
    assert saved_data == {"key": "value"}


def test_update_config(tmp_path):
    """Test updating config."""
    config_path = str(tmp_path / "config.json")
    context = Context(config_path=config_path)
    
    context.update_config({"key1": "value1"})
    assert context.config == {"key1": "value1"}
    
    context.update_config({"key2": "value2"})
    assert context.config == {"key1": "value1", "key2": "value2"}


def test_add_to_history():
    """Test adding command to history."""
    context = Context()
    context.add_to_history("test command", "test result")
    
    assert len(context.history) == 1
    assert context.history[0]["command"] == "test command"
    assert context.history[0]["result"] == "test result"
    assert "timestamp" in context.history[0]


def test_get_history():
    """Test getting command history."""
    context = Context()
    
    # Add more than the default limit
    for i in range(15):
        context.add_to_history(f"command {i}", f"result {i}")
    
    # Default limit is 10
    history = context.get_history()
    assert len(history) == 10
    assert history[0]["command"] == "command 5"  # Last 10 of 15
    
    # Test custom limit
    history = context.get_history(5)
    assert len(history) == 5
    assert history[0]["command"] == "command 10"  # Last 5 of 15
