"""Pytest configuration and fixtures."""
import os
import sys
from pathlib import Path
from typing import Generator, <PERSON><PERSON>

import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON>

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from cli import app


@pytest.fixture
def cli_runner() -> TyperCliRunner:
    """Fixture for invoking command-line interfaces."""
    return TyperCliRunner()


@pytest.fixture
def tmp_workspace(tmp_path: Path) -> Path:
    """Create a temporary workspace with some test files."""
    # Create some test files
    (tmp_path / "test_file.txt").write_text(
        "This is a test file.\nWith some content.\nAnd more content."
    )
    (tmp_path / "test_script.py").write_text(
        "def hello():\n    print('Hello, World!')\n\nif __name__ == '__main__':\n    hello()"
    )
    
    # Create a subdirectory
    subdir = tmp_path / "subdir"
    subdir.mkdir()
    (subdir / "nested_file.txt").write_text("Nested file content")
    
    return tmp_path


@pytest.fixture
def mock_context(mocker):
    """Mock the Context class."""
    mock_ctx = mocker.MagicMock()
    mock_ctx.config = {}
    mock_ctx.history = []
    return mock_ctx


@pytest.fixture
def mock_tools(mocker):
    """Mock the tools module."""
    mock = mocker.patch("cline_cli.cli.execute_command")
    mock.return_value = "Command output"
    return mock
