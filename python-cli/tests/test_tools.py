"""Tests for Cline CLI tools."""
import os
import subprocess
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest

from tools import (
    execute_command,
    file_search,
    list_files,
    read_file,
    write_file,
)


class TestExecuteCommand:
    """Tests for execute_command function."""

    def test_execute_success(self):
        """Test successful command execution."""
        result = execute_command("echo hello")
        assert result.strip() == "hello"

    def test_execute_with_cwd(self, tmp_path):
        """Test command execution with custom working directory."""
        result = execute_command("pwd", cwd=str(tmp_path))
        assert result.strip() == str(tmp_path)

    @patch("subprocess.run")
    def test_execute_error(self, mock_run):
        """Test command execution with error."""
        mock_run.side_effect = subprocess.CalledProcessError(
            returncode=1, cmd="invalid-command", stderr="Command not found"
        )
        
        result = execute_command("invalid-command")
        assert "Command not found" in result


class TestFileSearch:
    """Tests for file_search function."""

    def test_search_in_files(self, tmp_path):
        """Test searching text in files."""
        # Create test files
        file1 = tmp_path / "file1.txt"
        file1.write_text("This is a test file\nWith some content")
        
        file2 = tmp_path / "file2.txt"
        file2.write_text("Another file\nWith different content")
        
        # Search for content
        results = file_search("content", str(tmp_path), "txt")
        
        # Should find matches in both files
        assert len(results) == 2
        assert any(str(file1) in str(r[0]) for r in results)
        assert any(str(file2) in str(r[0]) for r in results)

    def test_search_case_insensitive(self, tmp_path):
        """Test case-insensitive search."""
        test_file = tmp_path / "test.txt"
        test_file.write_text("CASE INSENSITIVE test")
        
        results = file_search("insensitive", str(tmp_path), "txt")
        assert len(results) == 1
        assert "INSENSITIVE" in results[0][2]

    def test_search_nonexistent_dir(self):
        """Test search in non-existent directory."""
        with pytest.raises(FileNotFoundError):
            file_search("test", "/nonexistent/directory", "txt")


class TestFileOperations:
    """Tests for file operations."""

    def test_read_write_file(self, tmp_path):
        """Test reading and writing files."""
        test_file = tmp_path / "test.txt"
        test_content = "Test content"
        
        # Test write
        write_file(str(test_file), test_content)
        assert test_file.exists()
        
        # Test read
        content = read_file(str(test_file))
        assert content == test_content
    
    def test_read_nonexistent_file(self):
        """Test reading non-existent file."""
        with pytest.raises(FileNotFoundError):
            read_file("/nonexistent/file.txt")
    
    def test_write_to_nonexistent_dir(self, tmp_path):
        """Test writing to a non-existent directory."""
        test_file = tmp_path / "nonexistent" / "test.txt"
        write_file(str(test_file), "test")
        assert test_file.exists()


class TestListFiles:
    """Tests for list_files function."""

    def test_list_files(self, tmp_path):
        """Test listing files with pattern matching."""
        # Create test files
        (tmp_path / "file1.txt").touch()
        (tmp_path / "file2.py").touch()
        (tmp_path / "subdir").mkdir()
        (tmp_path / "subdir" / "file3.txt").touch()
        
        # Test listing all files
        files = list_files(str(tmp_path), "*")
        assert len(files) == 3  # file1.txt, file2.py, subdir/file3.txt
        
        # Test filtering by extension
        py_files = list_files(str(tmp_path), "*.py")
        assert len(py_files) == 1
        assert py_files[0].name == "file2.py"
        
        # Test recursive search
        txt_files = list_files(str(tmp_path), "**/*.txt")
        assert len(txt_files) == 2  # file1.txt, subdir/file3.txt
    
    def test_list_files_nonexistent_dir(self):
        """Test listing files in non-existent directory."""
        with pytest.raises(FileNotFoundError):
            list_files("/nonexistent/directory", "*")


class TestFileEncodings:
    """Tests for file encoding handling."""

    def test_read_non_utf8_file(self, tmp_path):
        """Test reading non-UTF-8 encoded file."""
        test_file = tmp_path / "test.txt"
        # Write binary data that's not valid UTF-8
        test_file.write_bytes(b'\x80abc')
        
        # Should not raise an exception
        content = read_file(str(test_file))
        assert content is not None

    def test_write_special_chars(self, tmp_path):
        """Test writing files with special characters."""
        test_file = tmp_path / "special.txt"
        test_content = "Special chars: äöüßéñ"
        
        write_file(str(test_file), test_content)
        assert read_file(str(test_file)) == test_content
