"""Tools for Cline CLI."""
from pathlib import Path
from typing import List, Tuple, Optional, Dict, Any, Union
import subprocess
import os
import shutil
import mimetypes
import json
import re

def execute_command(command: str, cwd: Optional[str] = None, timeout: int = 30) -> str:
    """Execute a shell command and return its output.

    Args:
        command: The command to execute
        cwd: Working directory for the command
        timeout: Command timeout in seconds

    Returns:
        Command output as string
    """
    try:
        result = subprocess.run(
            command,
            shell=True,
            text=True,
            capture_output=True,
            cwd=cwd,
            timeout=timeout
        )

        output = ""
        if result.stdout:
            output += result.stdout
        if result.stderr:
            output += f"\nSTDERR:\n{result.stderr}"

        if result.returncode == 0:
            return output or "Command executed successfully (no output)"
        else:
            return f"Command failed with exit code {result.returncode}:\n{output}"

    except subprocess.TimeoutExpired:
        return f"Command timed out after {timeout} seconds"
    except Exception as e:
        return f"Error executing command: {str(e)}"


def file_search(
    query: str,
    path: str = ".",
    file_extensions: Optional[List[str]] = None,
    case_sensitive: bool = False,
    max_results: int = 100
) -> str:
    """Search for text in files.

    Args:
        query: Text to search for
        path: Directory to search in
        file_extensions: List of file extensions to include (e.g., ['.py', '.js'])
        case_sensitive: Whether search should be case sensitive
        max_results: Maximum number of results to return

    Returns:
        Formatted search results as string
    """
    try:
        results = []
        search_path = Path(path).resolve()

        if not search_path.exists():
            return f"Error: Path '{path}' does not exist"

        # Default extensions if none provided
        if file_extensions is None:
            file_extensions = ['.py', '.js', '.ts', '.txt', '.md', '.json', '.yaml', '.yml', '.html', '.css']

        file_count = 0
        for file_path in search_path.rglob('*'):
            if file_path.is_file() and (not file_extensions or file_path.suffix in file_extensions):
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()

                    matching_lines = []
                    lines = content.split('\n')

                    for i, line in enumerate(lines, 1):
                        search_line = line if case_sensitive else line.lower()
                        search_query = query if case_sensitive else query.lower()
                        if search_query in search_line:
                            matching_lines.append((i, line.strip()))

                    if matching_lines:
                        results.append(f"\n📄 {file_path}:")
                        for line_num, line_content in matching_lines[:5]:  # Limit to first 5 matches per file
                            results.append(f"  {line_num:4d}: {line_content}")

                        if len(matching_lines) > 5:
                            results.append(f"  ... and {len(matching_lines) - 5} more matches")

                        file_count += 1
                        if file_count >= max_results:
                            break

                except (UnicodeDecodeError, PermissionError):
                    continue

        if results:
            return f"Found matches in {file_count} files for '{query}':\n" + "\n".join(results)
        else:
            return f"No matches found for '{query}' in {path}"

    except Exception as e:
        return f"Error searching for pattern: {str(e)}"


def read_file(file_path: str, encoding: str = 'utf-8') -> str:
    """Read the contents of a file.

    Args:
        file_path: Path to the file
        encoding: File encoding (default: utf-8)

    Returns:
        File contents as string
    """
    try:
        path = Path(file_path).expanduser().resolve()
        if not path.exists():
            return f"Error: File '{file_path}' not found"

        if not path.is_file():
            return f"Error: '{file_path}' is not a file"

        # Check if file is binary
        mime_type, _ = mimetypes.guess_type(file_path)
        if mime_type and not mime_type.startswith('text/'):
            return f"Error: '{file_path}' appears to be a binary file"

        content = path.read_text(encoding=encoding)

        # Add file info
        file_size = path.stat().st_size
        line_count = content.count('\n') + 1 if content else 0

        return f"File: {file_path} ({file_size} bytes, {line_count} lines)\n\n{content}"

    except UnicodeDecodeError:
        if encoding == 'utf-8':
            # Try with latin-1 encoding
            return read_file(file_path, 'latin-1')
        else:
            return f"Error: Unable to decode '{file_path}' with {encoding} encoding"
    except PermissionError:
        return f"Error: Permission denied reading '{file_path}'"
    except Exception as e:
        return f"Error reading file '{file_path}': {str(e)}"


def write_file(file_path: str, content: str, encoding: str = 'utf-8', backup: bool = True) -> str:
    """Write content to a file.

    Args:
        file_path: Path to the file
        content: Content to write
        encoding: File encoding (default: utf-8)
        backup: Whether to create a backup if file exists

    Returns:
        Success message or error
    """
    try:
        path = Path(file_path).expanduser().resolve()

        # Create backup if file exists and backup is requested
        if backup and path.exists():
            backup_path = path.with_suffix(path.suffix + '.bak')
            shutil.copy2(path, backup_path)

        # Create directory if it doesn't exist
        path.parent.mkdir(parents=True, exist_ok=True)

        path.write_text(content, encoding=encoding)

        file_size = path.stat().st_size
        line_count = content.count('\n') + 1 if content else 0

        return f"Successfully wrote to '{file_path}' ({file_size} bytes, {line_count} lines)"

    except PermissionError:
        return f"Error: Permission denied writing to '{file_path}'"
    except Exception as e:
        return f"Error writing to file '{file_path}': {str(e)}"


def list_files(directory: str = ".", recursive: bool = False, show_hidden: bool = False) -> str:
    """List files in a directory.

    Args:
        directory: Directory to list files from
        recursive: Whether to list files recursively
        show_hidden: Whether to show hidden files

    Returns:
        Formatted list of files and directories
    """
    try:
        path = Path(directory).expanduser().resolve()

        if not path.exists():
            return f"Error: Directory '{directory}' does not exist"

        if not path.is_dir():
            return f"Error: '{directory}' is not a directory"

        results = []
        file_count = 0
        dir_count = 0

        if recursive:
            for item in path.rglob('*'):
                if not show_hidden and item.name.startswith('.'):
                    continue

                if item.is_file():
                    size = item.stat().st_size
                    results.append(f"📄 {item} ({size} bytes)")
                    file_count += 1
                elif item.is_dir():
                    results.append(f"📁 {item}/")
                    dir_count += 1
        else:
            for item in path.iterdir():
                if not show_hidden and item.name.startswith('.'):
                    continue

                if item.is_file():
                    size = item.stat().st_size
                    results.append(f"📄 {item.name} ({size} bytes)")
                    file_count += 1
                elif item.is_dir():
                    results.append(f"📁 {item.name}/")
                    dir_count += 1

        if results:
            results.sort()
            header = f"Contents of '{directory}' ({dir_count} directories, {file_count} files):\n"
            return header + "\n".join(results)
        else:
            return f"Directory '{directory}' is empty"

    except PermissionError:
        return f"Error: Permission denied accessing '{directory}'"
    except Exception as e:
        return f"Error listing files: {str(e)}"


def create_directory(directory_path: str) -> str:
    """Create a directory and any necessary parent directories.

    Args:
        directory_path: Path to the directory to create

    Returns:
        Success message or error
    """
    try:
        path = Path(directory_path).expanduser().resolve()
        path.mkdir(parents=True, exist_ok=True)
        return f"Successfully created directory '{directory_path}'"
    except PermissionError:
        return f"Error: Permission denied creating directory '{directory_path}'"
    except Exception as e:
        return f"Error creating directory '{directory_path}': {str(e)}"


def delete_file(file_path: str) -> str:
    """Delete a file.

    Args:
        file_path: Path to the file to delete

    Returns:
        Success message or error
    """
    try:
        path = Path(file_path).expanduser().resolve()

        if not path.exists():
            return f"Error: File '{file_path}' does not exist"

        if not path.is_file():
            return f"Error: '{file_path}' is not a file"

        path.unlink()
        return f"Successfully deleted file '{file_path}'"

    except PermissionError:
        return f"Error: Permission denied deleting '{file_path}'"
    except Exception as e:
        return f"Error deleting file '{file_path}': {str(e)}"


def regex_search_files(
    pattern: str,
    path: str = ".",
    file_extensions: Optional[List[str]] = None,
    case_sensitive: bool = False,
    max_results: int = 100,
    context_lines: int = 2
) -> str:
    """Advanced regex search in files (ripgrep-like functionality).

    Args:
        pattern: Regular expression pattern to search for
        path: Directory to search in
        file_extensions: List of file extensions to include
        case_sensitive: Whether search should be case sensitive
        max_results: Maximum number of results to return
        context_lines: Number of context lines to show around matches

    Returns:
        Formatted search results as string
    """
    try:
        import re as regex_module

        results = []
        search_path = Path(path).resolve()

        if not search_path.exists():
            return f"Error: Path '{path}' does not exist"

        # Default extensions if none provided
        if file_extensions is None:
            file_extensions = ['.py', '.js', '.ts', '.txt', '.md', '.json', '.yaml', '.yml', '.html', '.css', '.sh', '.c', '.cpp', '.h', '.java', '.go', '.rs', '.php', '.rb']

        # Compile regex pattern
        try:
            flags = 0 if case_sensitive else regex_module.IGNORECASE
            pattern_re = regex_module.compile(pattern, flags)
        except regex_module.error as e:
            return f"Error: Invalid regex pattern: {e}"

        file_count = 0
        total_matches = 0

        for file_path in search_path.rglob('*'):
            if file_path.is_file() and (not file_extensions or file_path.suffix in file_extensions):
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()

                    lines = content.split('\n')
                    matching_lines = []

                    for i, line in enumerate(lines):
                        if pattern_re.search(line):
                            # Add context lines
                            start_line = max(0, i - context_lines)
                            end_line = min(len(lines), i + context_lines + 1)

                            context = []
                            for j in range(start_line, end_line):
                                prefix = ">" if j == i else " "
                                context.append(f"{prefix} {j+1:4d}: {lines[j]}")

                            matching_lines.append({
                                'line_num': i + 1,
                                'line': line.strip(),
                                'context': context
                            })
                            total_matches += 1

                    if matching_lines:
                        results.append(f"\n📄 {file_path} ({len(matching_lines)} matches):")

                        for match in matching_lines[:5]:  # Limit matches per file
                            results.append(f"  Match at line {match['line_num']}:")
                            for context_line in match['context']:
                                results.append(f"    {context_line}")
                            results.append("")

                        if len(matching_lines) > 5:
                            results.append(f"  ... and {len(matching_lines) - 5} more matches")

                        file_count += 1
                        if file_count >= max_results:
                            break

                except (UnicodeDecodeError, PermissionError):
                    continue

        if results:
            header = f"Found {total_matches} matches in {file_count} files for pattern '{pattern}':\n"
            return header + "\n".join(results)
        else:
            return f"No matches found for pattern '{pattern}' in {path}"

    except Exception as e:
        return f"Error searching for pattern: {str(e)}"


def extract_text_from_file(file_path: str) -> str:
    """Extract text content from various file types.

    Args:
        file_path: Path to the file

    Returns:
        Extracted text content
    """
    try:
        path = Path(file_path)
        if not path.exists():
            return f"Error: File '{file_path}' not found"

        # Handle different file types
        suffix = path.suffix.lower()

        if suffix in ['.txt', '.md', '.py', '.js', '.ts', '.html', '.css', '.json', '.yaml', '.yml', '.xml']:
            # Text files
            return read_file(file_path)

        elif suffix == '.pdf':
            # PDF files (requires PyPDF2 or similar)
            try:
                import PyPDF2
                with open(file_path, 'rb') as f:
                    reader = PyPDF2.PdfReader(f)
                    text = ""
                    for page in reader.pages:
                        text += page.extract_text() + "\n"
                return text
            except ImportError:
                return "Error: PyPDF2 required for PDF extraction. Install with: pip install PyPDF2"

        elif suffix in ['.docx']:
            # Word documents (requires python-docx)
            try:
                import docx
                doc = docx.Document(file_path)
                text = ""
                for paragraph in doc.paragraphs:
                    text += paragraph.text + "\n"
                return text
            except ImportError:
                return "Error: python-docx required for DOCX extraction. Install with: pip install python-docx"

        else:
            # Try to read as text
            return read_file(file_path)

    except Exception as e:
        return f"Error extracting text from '{file_path}': {str(e)}"


def get_folder_size(directory_path: str) -> str:
    """Get the total size of a directory.

    Args:
        directory_path: Path to the directory

    Returns:
        Formatted size information
    """
    try:
        path = Path(directory_path)
        if not path.exists():
            return f"Error: Directory '{directory_path}' does not exist"

        if not path.is_dir():
            return f"Error: '{directory_path}' is not a directory"

        total_size = 0
        file_count = 0
        dir_count = 0

        for item in path.rglob('*'):
            if item.is_file():
                total_size += item.stat().st_size
                file_count += 1
            elif item.is_dir():
                dir_count += 1

        # Format size
        def format_size(size_bytes):
            if size_bytes < 1024:
                return f"{size_bytes} B"
            elif size_bytes < 1024 * 1024:
                return f"{size_bytes / 1024:.1f} KB"
            elif size_bytes < 1024 * 1024 * 1024:
                return f"{size_bytes / (1024 * 1024):.1f} MB"
            else:
                return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

        return f"Directory: {directory_path}\nSize: {format_size(total_size)}\nFiles: {file_count}\nDirectories: {dir_count}"

    except Exception as e:
        return f"Error getting folder size for '{directory_path}': {str(e)}"


def diff_files(file1_path: str, file2_path: str, context_lines: int = 3, side_by_side: bool = False) -> str:
    """Compare two files and show visual diff.

    Args:
        file1_path: Path to the first file (old version)
        file2_path: Path to the second file (new version)
        context_lines: Number of context lines to show around changes
        side_by_side: Whether to show side-by-side diff view

    Returns:
        Formatted diff as string
    """
    try:
        from diff_viewer import TerminalDiffViewer

        viewer = TerminalDiffViewer(context_lines=context_lines)
        file_diff = viewer.compare_files(file1_path, file2_path)

        if side_by_side:
            return viewer.create_side_by_side_diff(file_diff)
        else:
            return viewer.display_diff(file_diff)

    except Exception as e:
        return f"Error comparing files: {str(e)}"


def diff_strings(old_content: str, new_content: str, file_path: str = "content", side_by_side: bool = False) -> str:
    """Compare two strings and show visual diff.

    Args:
        old_content: Original content
        new_content: New content
        file_path: Virtual file path for display
        side_by_side: Whether to show side-by-side diff view

    Returns:
        Formatted diff as string
    """
    try:
        from diff_viewer import TerminalDiffViewer

        viewer = TerminalDiffViewer()
        file_diff = viewer.compare_strings(old_content, new_content, file_path)

        if side_by_side:
            return viewer.create_side_by_side_diff(file_diff)
        else:
            return viewer.display_diff(file_diff)

    except Exception as e:
        return f"Error comparing strings: {str(e)}"


def git_diff(file_path: str, staged: bool = False) -> str:
    """Show git diff for a file with visual formatting.

    Args:
        file_path: Path to the file to show diff for
        staged: Whether to show staged changes

    Returns:
        Formatted git diff as string
    """
    try:
        from diff_viewer import show_git_diff
        return show_git_diff(file_path, staged)

    except Exception as e:
        return f"Error getting git diff: {str(e)}"
