"""Terminal-based diff viewer for Cline CLI.

This module provides visual diff capabilities in the terminal,
serving as an alternative to GUI-based diff viewers.
"""
import difflib
import os
import tempfile
from pathlib import Path
from typing import List, Optional, Tuple, Dict, Any
from dataclasses import dataclass
from enum import Enum

try:
    from rich.console import Console
    from rich.syntax import Syntax
    from rich.panel import Panel
    from rich.columns import Columns
    from rich.text import Text
    from rich.table import Table
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False


class DiffType(Enum):
    """Types of diff operations."""
    ADDED = "added"
    REMOVED = "removed"
    MODIFIED = "modified"
    UNCHANGED = "unchanged"


@dataclass
class DiffLine:
    """Represents a line in a diff."""
    line_number_old: Optional[int]
    line_number_new: Optional[int]
    content: str
    diff_type: DiffType
    is_highlighted: bool = False


@dataclass
class FileDiff:
    """Represents a diff for a single file."""
    file_path: str
    old_content: Optional[str]
    new_content: Optional[str]
    diff_lines: List[DiffLine]
    stats: Dict[str, int]  # added, removed, modified lines


class TerminalDiffViewer:
    """Terminal-based diff viewer with rich formatting."""
    
    def __init__(self, use_color: bool = True, context_lines: int = 3):
        """Initialize the diff viewer.
        
        Args:
            use_color: Whether to use colored output
            context_lines: Number of context lines to show around changes
        """
        self.use_color = use_color and RICH_AVAILABLE
        self.context_lines = context_lines
        self.console = Console() if RICH_AVAILABLE else None
        
        # Color scheme
        self.colors = {
            'added': 'green',
            'removed': 'red',
            'modified': 'yellow',
            'unchanged': 'white',
            'line_number': 'blue',
            'separator': 'cyan'
        }
    
    def compare_files(self, file1_path: str, file2_path: str) -> FileDiff:
        """Compare two files and generate a diff.
        
        Args:
            file1_path: Path to the first file (old version)
            file2_path: Path to the second file (new version)
            
        Returns:
            FileDiff object containing the comparison results
        """
        try:
            # Read file contents
            old_content = self._read_file(file1_path) if file1_path else ""
            new_content = self._read_file(file2_path) if file2_path else ""
            
            # Generate diff
            diff_lines = self._generate_diff_lines(old_content, new_content)
            
            # Calculate statistics
            stats = self._calculate_stats(diff_lines)
            
            return FileDiff(
                file_path=file2_path or file1_path,
                old_content=old_content,
                new_content=new_content,
                diff_lines=diff_lines,
                stats=stats
            )
        
        except Exception as e:
            return FileDiff(
                file_path=file2_path or file1_path,
                old_content=None,
                new_content=None,
                diff_lines=[],
                stats={'error': str(e)}
            )
    
    def compare_strings(self, old_content: str, new_content: str, file_path: str = "content") -> FileDiff:
        """Compare two strings and generate a diff.
        
        Args:
            old_content: Original content
            new_content: New content
            file_path: Virtual file path for display
            
        Returns:
            FileDiff object containing the comparison results
        """
        diff_lines = self._generate_diff_lines(old_content, new_content)
        stats = self._calculate_stats(diff_lines)
        
        return FileDiff(
            file_path=file_path,
            old_content=old_content,
            new_content=new_content,
            diff_lines=diff_lines,
            stats=stats
        )
    
    def display_diff(self, file_diff: FileDiff, show_line_numbers: bool = True) -> str:
        """Display a diff in the terminal.
        
        Args:
            file_diff: FileDiff object to display
            show_line_numbers: Whether to show line numbers
            
        Returns:
            Formatted diff string
        """
        if 'error' in file_diff.stats:
            return f"Error: {file_diff.stats['error']}"
        
        if not file_diff.diff_lines:
            return "No differences found."
        
        if self.use_color and self.console:
            return self._display_rich_diff(file_diff, show_line_numbers)
        else:
            return self._display_plain_diff(file_diff, show_line_numbers)
    
    def _read_file(self, file_path: str) -> str:
        """Read file content safely.
        
        Args:
            file_path: Path to the file
            
        Returns:
            File content as string
        """
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                return f.read()
        except Exception:
            return ""
    
    def _generate_diff_lines(self, old_content: str, new_content: str) -> List[DiffLine]:
        """Generate diff lines from two content strings.
        
        Args:
            old_content: Original content
            new_content: New content
            
        Returns:
            List of DiffLine objects
        """
        old_lines = old_content.splitlines() if old_content else []
        new_lines = new_content.splitlines() if new_content else []
        
        diff_lines = []
        
        # Use difflib to generate the diff
        differ = difflib.unified_diff(
            old_lines,
            new_lines,
            lineterm='',
            n=self.context_lines
        )
        
        old_line_num = 1
        new_line_num = 1
        
        for line in differ:
            if line.startswith('@@'):
                # Parse hunk header
                parts = line.split()
                if len(parts) >= 3:
                    old_range = parts[1][1:]  # Remove '-'
                    new_range = parts[2][1:]  # Remove '+'
                    
                    if ',' in old_range:
                        old_line_num = int(old_range.split(',')[0])
                    else:
                        old_line_num = int(old_range)
                    
                    if ',' in new_range:
                        new_line_num = int(new_range.split(',')[0])
                    else:
                        new_line_num = int(new_range)
                continue
            
            elif line.startswith('---') or line.startswith('+++'):
                # Skip file headers
                continue
            
            elif line.startswith('-'):
                # Removed line
                diff_lines.append(DiffLine(
                    line_number_old=old_line_num,
                    line_number_new=None,
                    content=line[1:],
                    diff_type=DiffType.REMOVED
                ))
                old_line_num += 1
            
            elif line.startswith('+'):
                # Added line
                diff_lines.append(DiffLine(
                    line_number_old=None,
                    line_number_new=new_line_num,
                    content=line[1:],
                    diff_type=DiffType.ADDED
                ))
                new_line_num += 1
            
            elif line.startswith(' '):
                # Unchanged line
                diff_lines.append(DiffLine(
                    line_number_old=old_line_num,
                    line_number_new=new_line_num,
                    content=line[1:],
                    diff_type=DiffType.UNCHANGED
                ))
                old_line_num += 1
                new_line_num += 1
        
        return diff_lines
    
    def _calculate_stats(self, diff_lines: List[DiffLine]) -> Dict[str, int]:
        """Calculate diff statistics.
        
        Args:
            diff_lines: List of diff lines
            
        Returns:
            Dictionary with statistics
        """
        stats = {
            'added': 0,
            'removed': 0,
            'unchanged': 0,
            'total_lines': len(diff_lines)
        }
        
        for line in diff_lines:
            if line.diff_type == DiffType.ADDED:
                stats['added'] += 1
            elif line.diff_type == DiffType.REMOVED:
                stats['removed'] += 1
            elif line.diff_type == DiffType.UNCHANGED:
                stats['unchanged'] += 1
        
        return stats
    
    def _display_rich_diff(self, file_diff: FileDiff, show_line_numbers: bool) -> str:
        """Display diff using rich formatting.
        
        Args:
            file_diff: FileDiff object
            show_line_numbers: Whether to show line numbers
            
        Returns:
            Formatted diff string
        """
        if not self.console:
            return self._display_plain_diff(file_diff, show_line_numbers)
        
        # Create header
        stats = file_diff.stats
        header_text = f"📄 {file_diff.file_path}"
        if stats.get('added', 0) > 0 or stats.get('removed', 0) > 0:
            header_text += f" (+{stats.get('added', 0)} -{stats.get('removed', 0)})"
        
        header = Panel(header_text, style="bold blue")
        
        # Create diff content
        diff_content = []
        
        for line in file_diff.diff_lines:
            line_text = Text()
            
            # Add line numbers
            if show_line_numbers:
                old_num = str(line.line_number_old) if line.line_number_old else " "
                new_num = str(line.line_number_new) if line.line_number_new else " "
                line_text.append(f"{old_num:>4} {new_num:>4} ", style=self.colors['line_number'])
            
            # Add diff indicator
            if line.diff_type == DiffType.ADDED:
                line_text.append("+ ", style=self.colors['added'])
                line_text.append(line.content, style=self.colors['added'])
            elif line.diff_type == DiffType.REMOVED:
                line_text.append("- ", style=self.colors['removed'])
                line_text.append(line.content, style=self.colors['removed'])
            elif line.diff_type == DiffType.UNCHANGED:
                line_text.append("  ", style=self.colors['unchanged'])
                line_text.append(line.content, style=self.colors['unchanged'])
            
            diff_content.append(line_text)
        
        # Render to string
        with self.console.capture() as capture:
            self.console.print(header)
            for line_text in diff_content:
                self.console.print(line_text)
        
        return capture.get()
    
    def _display_plain_diff(self, file_diff: FileDiff, show_line_numbers: bool) -> str:
        """Display diff using plain text formatting.
        
        Args:
            file_diff: FileDiff object
            show_line_numbers: Whether to show line numbers
            
        Returns:
            Formatted diff string
        """
        lines = []
        
        # Header
        stats = file_diff.stats
        header = f"=== {file_diff.file_path}"
        if stats.get('added', 0) > 0 or stats.get('removed', 0) > 0:
            header += f" (+{stats.get('added', 0)} -{stats.get('removed', 0)})"
        header += " ==="
        lines.append(header)
        lines.append("")
        
        # Diff content
        for line in file_diff.diff_lines:
            line_parts = []
            
            # Add line numbers
            if show_line_numbers:
                old_num = str(line.line_number_old) if line.line_number_old else " "
                new_num = str(line.line_number_new) if line.line_number_new else " "
                line_parts.append(f"{old_num:>4} {new_num:>4}")
            
            # Add diff indicator and content
            if line.diff_type == DiffType.ADDED:
                line_parts.append(f"+ {line.content}")
            elif line.diff_type == DiffType.REMOVED:
                line_parts.append(f"- {line.content}")
            elif line.diff_type == DiffType.UNCHANGED:
                line_parts.append(f"  {line.content}")
            
            lines.append(" ".join(line_parts))
        
        return "\n".join(lines)
    
    def create_side_by_side_diff(self, file_diff: FileDiff) -> str:
        """Create a side-by-side diff view.
        
        Args:
            file_diff: FileDiff object
            
        Returns:
            Side-by-side formatted diff string
        """
        if not file_diff.old_content and not file_diff.new_content:
            return "No content to compare."
        
        old_lines = file_diff.old_content.splitlines() if file_diff.old_content else []
        new_lines = file_diff.new_content.splitlines() if file_diff.new_content else []
        
        max_lines = max(len(old_lines), len(new_lines))
        
        # Pad shorter list
        while len(old_lines) < max_lines:
            old_lines.append("")
        while len(new_lines) < max_lines:
            new_lines.append("")
        
        lines = []
        lines.append(f"{'OLD':^40} | {'NEW':^40}")
        lines.append("-" * 40 + " | " + "-" * 40)
        
        for i, (old_line, new_line) in enumerate(zip(old_lines, new_lines)):
            old_display = old_line[:37] + "..." if len(old_line) > 40 else old_line
            new_display = new_line[:37] + "..." if len(new_line) > 40 else new_line
            
            # Determine if lines are different
            marker = "!" if old_line != new_line else " "
            
            lines.append(f"{old_display:<40} {marker} {new_display:<40}")
        
        return "\n".join(lines)


def show_file_diff(file1_path: str, file2_path: str, context_lines: int = 3) -> str:
    """Show diff between two files.
    
    Args:
        file1_path: Path to the first file (old version)
        file2_path: Path to the second file (new version)
        context_lines: Number of context lines to show
        
    Returns:
        Formatted diff string
    """
    viewer = TerminalDiffViewer(context_lines=context_lines)
    file_diff = viewer.compare_files(file1_path, file2_path)
    return viewer.display_diff(file_diff)


def show_string_diff(old_content: str, new_content: str, file_path: str = "content") -> str:
    """Show diff between two strings.
    
    Args:
        old_content: Original content
        new_content: New content
        file_path: Virtual file path for display
        
    Returns:
        Formatted diff string
    """
    viewer = TerminalDiffViewer()
    file_diff = viewer.compare_strings(old_content, new_content, file_path)
    return viewer.display_diff(file_diff)


def show_git_diff(file_path: str, staged: bool = False) -> str:
    """Show git diff for a file.
    
    Args:
        file_path: Path to the file
        staged: Whether to show staged changes
        
    Returns:
        Formatted diff string
    """
    import subprocess
    
    try:
        # Get git diff
        cmd = ["git", "diff"]
        if staged:
            cmd.append("--staged")
        cmd.append(file_path)
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            return f"Error: {result.stderr}"
        
        if not result.stdout:
            return "No changes found."
        
        # Parse git diff output and format it
        lines = result.stdout.split('\n')
        formatted_lines = []
        
        for line in lines:
            if line.startswith('+++') or line.startswith('---'):
                formatted_lines.append(f"📄 {line}")
            elif line.startswith('@@'):
                formatted_lines.append(f"🔍 {line}")
            elif line.startswith('+'):
                formatted_lines.append(f"✅ {line}")
            elif line.startswith('-'):
                formatted_lines.append(f"❌ {line}")
            else:
                formatted_lines.append(f"   {line}")
        
        return '\n'.join(formatted_lines)
    
    except Exception as e:
        return f"Error getting git diff: {str(e)}"
