# Cline Python CLI

A powerful command-line interface that brings the capabilities of the Cline VSCode extension to the terminal. This Python implementation provides AI-powered coding assistance, file operations, and task automation through a clean CLI interface.

## 🚀 Features

### Core Capabilities
- **AI-Powered Task Execution** - Complete tasks using OpenAI, Anthropic, and other LLM providers
- **Advanced File Operations** - Read, write, search, and manipulate files with intelligent context
- **Command Execution** - Run shell commands with proper error handling and output capture
- **Interactive Shell** - Full-featured interactive mode for continuous AI assistance
- **Task Management** - Create, track, and manage complex coding tasks
- **Migration Support** - Seamlessly migrate from the JavaScript Cline extension

### Enhanced Tools
- **Smart File Search** - Regex support, multiple file types, case-sensitive options
- **Context-Aware Operations** - Maintain conversation history and project context
- **Git Integration** - Repository status, commit message generation
- **Workspace Tracking** - Monitor file changes and project structure
- **Telemetry & Analytics** - Optional usage tracking and performance metrics

## 📦 Installation

### Quick Setup
```bash
cd python-cli
python setup.py
```

### Manual Installation
```bash
# Install dependencies
pip install -e .

# Create configuration
cp config.example.json config.json
# Edit config.json with your API keys
```

### Dependencies
- Python 3.8+
- aiohttp (for async API calls)
- typer (for CLI interface)
- rich (for enhanced terminal output)
- Additional dependencies listed in pyproject.toml

## ⚙️ Configuration

Create a `config.json` file with your settings:

```json
{
  "api_key": "your-openai-or-anthropic-key",
  "model": "gpt-4",
  "provider": "openai",
  "temperature": 0.7,
  "max_tokens": 4096,
  "telemetry_enabled": false,
  "auto_approval": false
}
```

### Supported Providers
- **OpenAI**: gpt-4, gpt-4-turbo, gpt-3.5-turbo
- **Anthropic**: claude-3-opus, claude-3-sonnet, claude-3-haiku
- **Others**: Extensible architecture for additional providers

## 🖥️ Usage

### Basic Commands

```bash
# Show help
python -m cli --help

# Execute a shell command
python -m cli run "ls -la"

# Search for text in files
python -m cli search "function main" --path src --extensions .py .js

# Read a file
python -m cli read path/to/file.py

# Write to a file
python -m cli write path/to/file.py "print('Hello, World!')"

# List files in directory
python -m cli list --recursive --show-hidden
```

### Interactive Mode

```bash
# Start interactive shell
python -m cli shell

# In the shell:
cline> search "TODO" --path src
cline> read main.py
cline> run "python test.py"
cline> exit
```

### Task Management

```bash
# Create a new task
python -m cli task create "Implement user authentication"

# List tasks
python -m cli task list

# Show task details
python -m cli task show <task-id>

# Chat with AI about a task
python -m cli chat <task-id>
```

### Migration from JavaScript

```bash
# Migrate settings and tasks from JS Cline
python -m cli migrate /path/to/js/cline/project

# This will copy:
# - VS Code settings → config.json
# - Task history → tasks/
# - Custom instructions → custom_instructions.txt
# - Prompt templates → prompts/
```

## 🔧 Advanced Usage

### Programmatic API

```python
from task_executor import TaskExecutor
from api import ApiConfiguration, ApiProvider

# Configure API
config = ApiConfiguration(
    api_key="your-key",
    model="gpt-4",
    provider=ApiProvider.OPENAI.value
)

# Execute a task
executor = TaskExecutor(config)
result = await executor.execute_task(
    "Create a Python function to calculate fibonacci numbers"
)
print(result)
```

### Custom Tools

```python
from tools import execute_command, read_file, write_file

# Execute commands
output = execute_command("git status", cwd="/path/to/repo")

# File operations
content = read_file("script.py")
write_file("output.txt", "Generated content", backup=True)

# Search operations
results = file_search("TODO", path="src", case_sensitive=False)
```

## 🛠️ Development

### Setup Development Environment

```bash
# Clone and setup
git clone <repository>
cd python-cli

# Install in development mode
pip install -e ".[dev]"

# Run tests
pytest

# Format code
black .
isort .

# Type checking
mypy .
```

### Project Structure

```
python-cli/
├── api.py              # API handlers for LLM providers
├── cli.py              # Main CLI interface
├── task_executor.py    # Core task execution engine
├── tools.py            # File and command tools
├── migration.py        # JS to Python migration
├── task_manager.py     # Task persistence and management
├── context.py          # Context and memory management
├── workspace.py        # Workspace tracking
├── git.py              # Git integration
├── telemetry.py        # Analytics and tracking
├── config.json         # Configuration file
├── tasks/              # Task storage
├── prompts/            # Prompt templates
└── tests/              # Test suite
```

### Adding New Features

1. **New Tools**: Add functions to `tools.py`
2. **API Providers**: Extend `api.py` with new handlers
3. **CLI Commands**: Add commands to `cli.py`
4. **Task Types**: Extend `task_executor.py`

## 🧪 Testing

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=. --cov-report=html

# Run specific test file
pytest tests/test_tools.py

# Run with verbose output
pytest -v
```

## 📊 Migration Status

This Python CLI implements most core features from the JavaScript Cline extension:

- ✅ **API Integration** (OpenAI, Anthropic)
- ✅ **Task Execution** (Tool calling, conversation management)
- ✅ **File Operations** (Enhanced with backup, encoding support)
- ✅ **Command Execution** (Timeout, error handling)
- ✅ **Migration Tools** (Settings, tasks, prompts)
- ⚠️ **Browser Integration** (Not applicable for CLI)
- ⚠️ **MCP Support** (Planned)
- ⚠️ **Visual Diff** (Terminal-based alternative)

See `MIGRATION_STATUS.md` for detailed progress.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

Apache 2.0 - See LICENSE file for details

## 🆘 Support

- **Issues**: Report bugs and request features
- **Documentation**: Check `MIGRATION_STATUS.md` for detailed status
- **Examples**: See `examples/` directory for usage examples

## 🔮 Roadmap

- [ ] MCP (Model Context Protocol) support
- [ ] Enhanced terminal UI with rich formatting
- [ ] Plugin system for extensibility
- [ ] Integration with more LLM providers
- [ ] Advanced code analysis and refactoring tools
- [ ] Collaborative features and team workflows
