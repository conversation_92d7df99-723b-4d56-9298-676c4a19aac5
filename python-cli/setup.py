#!/usr/bin/env python3
"""Setup script for Cline Python CLI."""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(f"   Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stderr:
            print(f"   Error: {e.stderr.strip()}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 3.8+ required, found {version.major}.{version.minor}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True


def install_dependencies():
    """Install required dependencies."""
    print("\n📦 Installing dependencies...")
    
    # Install in development mode
    if not run_command("pip install -e .", "Installing Cline CLI in development mode"):
        return False
    
    # Install optional development dependencies
    dev_deps = [
        "pytest>=7.0.0",
        "pytest-cov>=4.0.0", 
        "pytest-mock>=3.10.0",
        "black>=23.0.0",
        "isort>=5.12.0",
        "mypy>=1.0.0"
    ]
    
    for dep in dev_deps:
        run_command(f"pip install {dep}", f"Installing {dep}")
    
    return True


def create_config():
    """Create default configuration."""
    print("\n⚙️  Setting up configuration...")
    
    config_path = Path("config.json")
    if config_path.exists():
        print("✅ Configuration file already exists")
        return True
    
    default_config = {
        "api_key": "",
        "model": "gpt-4",
        "provider": "openai",
        "temperature": 0.7,
        "max_tokens": 4096,
        "telemetry_enabled": False,
        "auto_approval": False
    }
    
    try:
        import json
        with open(config_path, 'w') as f:
            json.dump(default_config, f, indent=2)
        print("✅ Created default configuration file")
        print("   Edit config.json to add your API key and preferences")
        return True
    except Exception as e:
        print(f"❌ Failed to create configuration: {e}")
        return False


def create_directories():
    """Create necessary directories."""
    print("\n📁 Creating directories...")
    
    directories = ["tasks", "prompts", "logs"]
    
    for directory in directories:
        try:
            Path(directory).mkdir(exist_ok=True)
            print(f"✅ Created {directory}/ directory")
        except Exception as e:
            print(f"❌ Failed to create {directory}/ directory: {e}")
            return False
    
    return True


def test_installation():
    """Test the installation."""
    print("\n🧪 Testing installation...")
    
    # Test import
    try:
        import cli
        print("✅ CLI module imports successfully")
    except ImportError as e:
        print(f"❌ Failed to import CLI module: {e}")
        return False
    
    # Test command
    if run_command("python -m cli --help", "Testing CLI command"):
        return True
    
    return False


def main():
    """Main setup function."""
    print("🚀 Cline Python CLI Setup")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed during dependency installation")
        sys.exit(1)
    
    # Create configuration
    if not create_config():
        print("\n⚠️  Warning: Failed to create configuration file")
    
    # Create directories
    if not create_directories():
        print("\n⚠️  Warning: Failed to create some directories")
    
    # Test installation
    if not test_installation():
        print("\n⚠️  Warning: Installation test failed")
    
    print("\n🎉 Setup completed!")
    print("\nNext steps:")
    print("1. Edit config.json to add your API key")
    print("2. Run: python -m cli --help")
    print("3. Try: python -m cli shell")
    print("\nFor migration from JS version:")
    print("   python -m cli migrate /path/to/js/cline")


if __name__ == "__main__":
    main()
