"""Browser automation session for Cline CLI.

This module provides browser automation capabilities using Playwright,
similar to the Puppeteer integration in the JavaScript version.
"""
import asyncio
import base64
import json
import tempfile
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

try:
    from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext
except ImportError:
    async_playwright = None
    Browser = None
    Page = None
    BrowserContext = None


@dataclass
class BrowserActionResult:
    """Result of a browser action."""
    screenshot: Optional[str] = None
    logs: Optional[str] = None
    current_url: Optional[str] = None
    current_mouse_position: Optional[str] = None
    success: bool = True
    error: Optional[str] = None


@dataclass
class BrowserSettings:
    """Browser configuration settings."""
    headless: bool = True
    viewport_width: int = 1280
    viewport_height: int = 720
    timeout: int = 30000
    user_agent: Optional[str] = None
    remote_browser_enabled: bool = False
    remote_browser_host: Optional[str] = None


class BrowserSession:
    """Browser automation session using <PERSON>wright."""
    
    def __init__(self, settings: BrowserSettings):
        """Initialize the browser session.
        
        Args:
            settings: Browser configuration settings
        """
        if async_playwright is None:
            raise ImportError("Playwright is required for browser automation. Install with: pip install playwright")
        
        self.settings = settings
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.console_logs: List[str] = []
        self.browser_actions: List[str] = []
        self.task_id: Optional[str] = None
    
    def set_task_id(self, task_id: str) -> None:
        """Set the task ID for telemetry tracking."""
        self.task_id = task_id
    
    async def launch_browser(self) -> BrowserActionResult:
        """Launch the browser."""
        try:
            self.playwright = await async_playwright().start()
            
            # Launch browser
            self.browser = await self.playwright.chromium.launch(
                headless=self.settings.headless,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            # Create context
            self.context = await self.browser.new_context(
                viewport={
                    'width': self.settings.viewport_width,
                    'height': self.settings.viewport_height
                },
                user_agent=self.settings.user_agent
            )
            
            # Create page
            self.page = await self.context.new_page()
            
            # Set up console logging
            self.page.on('console', self._handle_console_message)
            
            # Set default timeout
            self.page.set_default_timeout(self.settings.timeout)
            
            self.browser_actions.append("launch")
            
            return BrowserActionResult(
                logs="Browser launched successfully",
                success=True
            )
            
        except Exception as e:
            return BrowserActionResult(
                success=False,
                error=f"Failed to launch browser: {str(e)}"
            )
    
    async def navigate_to_url(self, url: str) -> BrowserActionResult:
        """Navigate to a URL."""
        if not self.page:
            return BrowserActionResult(
                success=False,
                error="Browser not launched. Call launch_browser() first."
            )
        
        try:
            await self.page.goto(url, wait_until='domcontentloaded')
            self.browser_actions.append(f"navigate:{url}")
            
            screenshot = await self._take_screenshot()
            current_url = self.page.url
            logs = self._get_recent_logs()
            
            return BrowserActionResult(
                screenshot=screenshot,
                logs=logs,
                current_url=current_url,
                success=True
            )
            
        except Exception as e:
            return BrowserActionResult(
                success=False,
                error=f"Failed to navigate to {url}: {str(e)}"
            )
    
    async def click(self, coordinate: str) -> BrowserActionResult:
        """Click at the specified coordinate.
        
        Args:
            coordinate: Coordinate in format "x,y"
        """
        if not self.page:
            return BrowserActionResult(
                success=False,
                error="Browser not launched. Call launch_browser() first."
            )
        
        try:
            # Parse coordinate
            x, y = map(int, coordinate.split(','))
            
            await self.page.mouse.click(x, y)
            self.browser_actions.append(f"click:{coordinate}")
            
            # Wait a bit for any page changes
            await asyncio.sleep(0.5)
            
            screenshot = await self._take_screenshot()
            current_url = self.page.url
            logs = self._get_recent_logs()
            
            return BrowserActionResult(
                screenshot=screenshot,
                logs=logs,
                current_url=current_url,
                current_mouse_position=coordinate,
                success=True
            )
            
        except Exception as e:
            return BrowserActionResult(
                success=False,
                error=f"Failed to click at {coordinate}: {str(e)}"
            )
    
    async def type_text(self, text: str) -> BrowserActionResult:
        """Type text at the current focus."""
        if not self.page:
            return BrowserActionResult(
                success=False,
                error="Browser not launched. Call launch_browser() first."
            )
        
        try:
            await self.page.keyboard.type(text)
            self.browser_actions.append(f"type:{len(text)} chars")
            
            # Wait a bit for any page changes
            await asyncio.sleep(0.3)
            
            screenshot = await self._take_screenshot()
            current_url = self.page.url
            logs = self._get_recent_logs()
            
            return BrowserActionResult(
                screenshot=screenshot,
                logs=logs,
                current_url=current_url,
                success=True
            )
            
        except Exception as e:
            return BrowserActionResult(
                success=False,
                error=f"Failed to type text: {str(e)}"
            )
    
    async def scroll_down(self) -> BrowserActionResult:
        """Scroll down the page."""
        if not self.page:
            return BrowserActionResult(
                success=False,
                error="Browser not launched. Call launch_browser() first."
            )
        
        try:
            await self.page.evaluate("window.scrollBy({ top: 600, behavior: 'auto' })")
            self.browser_actions.append("scroll_down")
            
            await asyncio.sleep(0.3)
            
            screenshot = await self._take_screenshot()
            current_url = self.page.url
            logs = self._get_recent_logs()
            
            return BrowserActionResult(
                screenshot=screenshot,
                logs=logs,
                current_url=current_url,
                success=True
            )
            
        except Exception as e:
            return BrowserActionResult(
                success=False,
                error=f"Failed to scroll down: {str(e)}"
            )
    
    async def scroll_up(self) -> BrowserActionResult:
        """Scroll up the page."""
        if not self.page:
            return BrowserActionResult(
                success=False,
                error="Browser not launched. Call launch_browser() first."
            )
        
        try:
            await self.page.evaluate("window.scrollBy({ top: -600, behavior: 'auto' })")
            self.browser_actions.append("scroll_up")
            
            await asyncio.sleep(0.3)
            
            screenshot = await self._take_screenshot()
            current_url = self.page.url
            logs = self._get_recent_logs()
            
            return BrowserActionResult(
                screenshot=screenshot,
                logs=logs,
                current_url=current_url,
                success=True
            )
            
        except Exception as e:
            return BrowserActionResult(
                success=False,
                error=f"Failed to scroll up: {str(e)}"
            )
    
    async def close_browser(self) -> BrowserActionResult:
        """Close the browser."""
        try:
            if self.page:
                await self.page.close()
                self.page = None
            
            if self.context:
                await self.context.close()
                self.context = None
            
            if self.browser:
                await self.browser.close()
                self.browser = None
            
            if self.playwright:
                await self.playwright.stop()
                self.playwright = None
            
            self.browser_actions.append("close")
            
            return BrowserActionResult(
                logs="Browser closed successfully",
                success=True
            )
            
        except Exception as e:
            return BrowserActionResult(
                success=False,
                error=f"Failed to close browser: {str(e)}"
            )
    
    async def _take_screenshot(self) -> Optional[str]:
        """Take a screenshot and return as base64 string."""
        if not self.page:
            return None
        
        try:
            screenshot_bytes = await self.page.screenshot(full_page=False)
            return base64.b64encode(screenshot_bytes).decode('utf-8')
        except Exception:
            return None
    
    def _handle_console_message(self, msg) -> None:
        """Handle console messages from the browser."""
        log_entry = f"[{msg.type}] {msg.text}"
        self.console_logs.append(log_entry)
        
        # Keep only recent logs (last 50)
        if len(self.console_logs) > 50:
            self.console_logs = self.console_logs[-50:]
    
    def _get_recent_logs(self) -> str:
        """Get recent console logs as a string."""
        if not self.console_logs:
            return ""
        
        # Return last 10 logs
        recent_logs = self.console_logs[-10:]
        return "\n".join(recent_logs)
    
    def get_connection_info(self) -> Dict[str, Any]:
        """Get browser connection information."""
        return {
            "isConnected": self.browser is not None,
            "isRemote": self.settings.remote_browser_enabled,
            "host": self.settings.remote_browser_host or "localhost"
        }
    
    async def dispose(self) -> None:
        """Dispose of the browser session."""
        await self.close_browser()
