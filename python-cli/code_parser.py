"""Code parsing utilities for Cline CLI.

This module provides basic code analysis and parsing functionality,
serving as a simplified alternative to tree-sitter.
"""
import ast
import re
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass


@dataclass
class CodeDefinition:
    """Represents a code definition (function, class, variable, etc.)."""
    name: str
    type: str  # 'function', 'class', 'variable', 'method', 'import'
    line_start: int
    line_end: int
    file_path: str
    signature: Optional[str] = None
    docstring: Optional[str] = None
    parent: Optional[str] = None  # For methods, the parent class


class CodeParser:
    """Basic code parser for various programming languages."""
    
    def __init__(self):
        """Initialize the code parser."""
        self.parsers = {
            '.py': self._parse_python,
            '.js': self._parse_javascript,
            '.ts': self._parse_typescript,
            '.java': self._parse_java,
            '.cpp': self._parse_cpp,
            '.c': self._parse_c,
            '.h': self._parse_c,
            '.go': self._parse_go,
            '.rs': self._parse_rust,
            '.php': self._parse_php,
            '.rb': self._parse_ruby,
        }
    
    def parse_file(self, file_path: str) -> List[CodeDefinition]:
        """Parse a file and extract code definitions.
        
        Args:
            file_path: Path to the file to parse
            
        Returns:
            List of code definitions found in the file
        """
        path = Path(file_path)
        if not path.exists():
            return []
        
        suffix = path.suffix.lower()
        if suffix not in self.parsers:
            return []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            return self.parsers[suffix](content, file_path)
        
        except Exception:
            return []
    
    def _parse_python(self, content: str, file_path: str) -> List[CodeDefinition]:
        """Parse Python code using AST."""
        definitions = []
        
        try:
            tree = ast.parse(content)
            lines = content.split('\n')
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    # Function definition
                    signature = self._get_python_function_signature(node, lines)
                    docstring = ast.get_docstring(node)
                    
                    definitions.append(CodeDefinition(
                        name=node.name,
                        type='function',
                        line_start=node.lineno,
                        line_end=node.end_lineno or node.lineno,
                        file_path=file_path,
                        signature=signature,
                        docstring=docstring
                    ))
                
                elif isinstance(node, ast.ClassDef):
                    # Class definition
                    docstring = ast.get_docstring(node)
                    
                    definitions.append(CodeDefinition(
                        name=node.name,
                        type='class',
                        line_start=node.lineno,
                        line_end=node.end_lineno or node.lineno,
                        file_path=file_path,
                        docstring=docstring
                    ))
                    
                    # Methods within the class
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            signature = self._get_python_function_signature(item, lines)
                            method_docstring = ast.get_docstring(item)
                            
                            definitions.append(CodeDefinition(
                                name=item.name,
                                type='method',
                                line_start=item.lineno,
                                line_end=item.end_lineno or item.lineno,
                                file_path=file_path,
                                signature=signature,
                                docstring=method_docstring,
                                parent=node.name
                            ))
                
                elif isinstance(node, ast.Import):
                    # Import statement
                    for alias in node.names:
                        definitions.append(CodeDefinition(
                            name=alias.name,
                            type='import',
                            line_start=node.lineno,
                            line_end=node.lineno,
                            file_path=file_path
                        ))
                
                elif isinstance(node, ast.ImportFrom):
                    # From import statement
                    module = node.module or ''
                    for alias in node.names:
                        definitions.append(CodeDefinition(
                            name=f"{module}.{alias.name}" if module else alias.name,
                            type='import',
                            line_start=node.lineno,
                            line_end=node.lineno,
                            file_path=file_path
                        ))
        
        except SyntaxError:
            # If AST parsing fails, fall back to regex
            return self._parse_python_regex(content, file_path)
        
        return definitions
    
    def _get_python_function_signature(self, node: ast.FunctionDef, lines: List[str]) -> str:
        """Extract function signature from AST node."""
        try:
            start_line = node.lineno - 1
            end_line = start_line
            
            # Find the end of the function signature
            for i in range(start_line, min(len(lines), start_line + 10)):
                if ':' in lines[i]:
                    end_line = i
                    break
            
            signature_lines = lines[start_line:end_line + 1]
            return ' '.join(line.strip() for line in signature_lines)
        
        except Exception:
            return f"def {node.name}(...)"
    
    def _parse_python_regex(self, content: str, file_path: str) -> List[CodeDefinition]:
        """Parse Python code using regex patterns."""
        definitions = []
        lines = content.split('\n')
        
        # Function definitions
        func_pattern = r'^(\s*)def\s+(\w+)\s*\('
        for i, line in enumerate(lines):
            match = re.match(func_pattern, line)
            if match:
                definitions.append(CodeDefinition(
                    name=match.group(2),
                    type='function',
                    line_start=i + 1,
                    line_end=i + 1,
                    file_path=file_path,
                    signature=line.strip()
                ))
        
        # Class definitions
        class_pattern = r'^(\s*)class\s+(\w+)'
        for i, line in enumerate(lines):
            match = re.match(class_pattern, line)
            if match:
                definitions.append(CodeDefinition(
                    name=match.group(2),
                    type='class',
                    line_start=i + 1,
                    line_end=i + 1,
                    file_path=file_path,
                    signature=line.strip()
                ))
        
        return definitions
    
    def _parse_javascript(self, content: str, file_path: str) -> List[CodeDefinition]:
        """Parse JavaScript code using regex patterns."""
        definitions = []
        lines = content.split('\n')
        
        # Function declarations
        patterns = [
            r'function\s+(\w+)\s*\(',  # function name()
            r'const\s+(\w+)\s*=\s*function',  # const name = function
            r'const\s+(\w+)\s*=\s*\(',  # const name = (
            r'(\w+)\s*:\s*function',  # name: function
            r'(\w+)\s*\([^)]*\)\s*=>',  # name() =>
            r'const\s+(\w+)\s*=\s*[^=]*=>'  # const name = ... =>
        ]
        
        for i, line in enumerate(lines):
            for pattern in patterns:
                match = re.search(pattern, line)
                if match:
                    definitions.append(CodeDefinition(
                        name=match.group(1),
                        type='function',
                        line_start=i + 1,
                        line_end=i + 1,
                        file_path=file_path,
                        signature=line.strip()
                    ))
                    break
        
        # Class definitions
        class_pattern = r'class\s+(\w+)'
        for i, line in enumerate(lines):
            match = re.search(class_pattern, line)
            if match:
                definitions.append(CodeDefinition(
                    name=match.group(1),
                    type='class',
                    line_start=i + 1,
                    line_end=i + 1,
                    file_path=file_path,
                    signature=line.strip()
                ))
        
        return definitions
    
    def _parse_typescript(self, content: str, file_path: str) -> List[CodeDefinition]:
        """Parse TypeScript code (similar to JavaScript)."""
        # TypeScript parsing is similar to JavaScript with type annotations
        definitions = self._parse_javascript(content, file_path)
        
        # Additional TypeScript-specific patterns
        lines = content.split('\n')
        
        # Interface definitions
        interface_pattern = r'interface\s+(\w+)'
        for i, line in enumerate(lines):
            match = re.search(interface_pattern, line)
            if match:
                definitions.append(CodeDefinition(
                    name=match.group(1),
                    type='interface',
                    line_start=i + 1,
                    line_end=i + 1,
                    file_path=file_path,
                    signature=line.strip()
                ))
        
        # Type definitions
        type_pattern = r'type\s+(\w+)\s*='
        for i, line in enumerate(lines):
            match = re.search(type_pattern, line)
            if match:
                definitions.append(CodeDefinition(
                    name=match.group(1),
                    type='type',
                    line_start=i + 1,
                    line_end=i + 1,
                    file_path=file_path,
                    signature=line.strip()
                ))
        
        return definitions
    
    def _parse_java(self, content: str, file_path: str) -> List[CodeDefinition]:
        """Parse Java code using regex patterns."""
        definitions = []
        lines = content.split('\n')
        
        # Class definitions
        class_pattern = r'(public|private|protected)?\s*class\s+(\w+)'
        for i, line in enumerate(lines):
            match = re.search(class_pattern, line)
            if match:
                definitions.append(CodeDefinition(
                    name=match.group(2),
                    type='class',
                    line_start=i + 1,
                    line_end=i + 1,
                    file_path=file_path,
                    signature=line.strip()
                ))
        
        # Method definitions
        method_pattern = r'(public|private|protected)?\s*(static)?\s*\w+\s+(\w+)\s*\('
        for i, line in enumerate(lines):
            match = re.search(method_pattern, line)
            if match:
                definitions.append(CodeDefinition(
                    name=match.group(3),
                    type='method',
                    line_start=i + 1,
                    line_end=i + 1,
                    file_path=file_path,
                    signature=line.strip()
                ))
        
        return definitions
    
    def _parse_cpp(self, content: str, file_path: str) -> List[CodeDefinition]:
        """Parse C++ code using regex patterns."""
        return self._parse_c(content, file_path)
    
    def _parse_c(self, content: str, file_path: str) -> List[CodeDefinition]:
        """Parse C/C++ code using regex patterns."""
        definitions = []
        lines = content.split('\n')
        
        # Function definitions
        func_pattern = r'^\s*\w+\s+(\w+)\s*\([^)]*\)\s*{'
        for i, line in enumerate(lines):
            match = re.search(func_pattern, line)
            if match:
                definitions.append(CodeDefinition(
                    name=match.group(1),
                    type='function',
                    line_start=i + 1,
                    line_end=i + 1,
                    file_path=file_path,
                    signature=line.strip()
                ))
        
        return definitions
    
    def _parse_go(self, content: str, file_path: str) -> List[CodeDefinition]:
        """Parse Go code using regex patterns."""
        definitions = []
        lines = content.split('\n')
        
        # Function definitions
        func_pattern = r'func\s+(\w+)\s*\('
        for i, line in enumerate(lines):
            match = re.search(func_pattern, line)
            if match:
                definitions.append(CodeDefinition(
                    name=match.group(1),
                    type='function',
                    line_start=i + 1,
                    line_end=i + 1,
                    file_path=file_path,
                    signature=line.strip()
                ))
        
        return definitions
    
    def _parse_rust(self, content: str, file_path: str) -> List[CodeDefinition]:
        """Parse Rust code using regex patterns."""
        definitions = []
        lines = content.split('\n')
        
        # Function definitions
        func_pattern = r'fn\s+(\w+)\s*\('
        for i, line in enumerate(lines):
            match = re.search(func_pattern, line)
            if match:
                definitions.append(CodeDefinition(
                    name=match.group(1),
                    type='function',
                    line_start=i + 1,
                    line_end=i + 1,
                    file_path=file_path,
                    signature=line.strip()
                ))
        
        return definitions
    
    def _parse_php(self, content: str, file_path: str) -> List[CodeDefinition]:
        """Parse PHP code using regex patterns."""
        definitions = []
        lines = content.split('\n')
        
        # Function definitions
        func_pattern = r'function\s+(\w+)\s*\('
        for i, line in enumerate(lines):
            match = re.search(func_pattern, line)
            if match:
                definitions.append(CodeDefinition(
                    name=match.group(1),
                    type='function',
                    line_start=i + 1,
                    line_end=i + 1,
                    file_path=file_path,
                    signature=line.strip()
                ))
        
        return definitions
    
    def _parse_ruby(self, content: str, file_path: str) -> List[CodeDefinition]:
        """Parse Ruby code using regex patterns."""
        definitions = []
        lines = content.split('\n')
        
        # Method definitions
        method_pattern = r'def\s+(\w+)'
        for i, line in enumerate(lines):
            match = re.search(method_pattern, line)
            if match:
                definitions.append(CodeDefinition(
                    name=match.group(1),
                    type='method',
                    line_start=i + 1,
                    line_end=i + 1,
                    file_path=file_path,
                    signature=line.strip()
                ))
        
        return definitions


def parse_source_code_for_definitions_top_level(file_path: str) -> List[Dict[str, Any]]:
    """Parse source code and return top-level definitions.
    
    Args:
        file_path: Path to the source file
        
    Returns:
        List of definition dictionaries
    """
    parser = CodeParser()
    definitions = parser.parse_file(file_path)
    
    # Convert to dictionary format for compatibility
    return [
        {
            'name': defn.name,
            'type': defn.type,
            'line_start': defn.line_start,
            'line_end': defn.line_end,
            'signature': defn.signature,
            'docstring': defn.docstring,
            'parent': defn.parent
        }
        for defn in definitions
    ]


def get_code_definitions_summary(directory_path: str) -> str:
    """Get a summary of code definitions in a directory.
    
    Args:
        directory_path: Path to the directory to analyze
        
    Returns:
        Formatted summary of code definitions
    """
    parser = CodeParser()
    all_definitions = []
    
    directory = Path(directory_path)
    if not directory.exists():
        return f"Error: Directory '{directory_path}' does not exist"
    
    # Supported file extensions
    supported_extensions = ['.py', '.js', '.ts', '.java', '.cpp', '.c', '.h', '.go', '.rs', '.php', '.rb']
    
    for file_path in directory.rglob('*'):
        if file_path.is_file() and file_path.suffix in supported_extensions:
            definitions = parser.parse_file(str(file_path))
            all_definitions.extend(definitions)
    
    if not all_definitions:
        return f"No code definitions found in '{directory_path}'"
    
    # Group by type
    by_type = {}
    for defn in all_definitions:
        if defn.type not in by_type:
            by_type[defn.type] = []
        by_type[defn.type].append(defn)
    
    # Format summary
    summary = [f"Code definitions in '{directory_path}':\n"]
    
    for def_type, definitions in by_type.items():
        summary.append(f"{def_type.title()}s ({len(definitions)}):")
        for defn in definitions[:10]:  # Limit to first 10
            relative_path = Path(defn.file_path).relative_to(directory)
            summary.append(f"  - {defn.name} ({relative_path}:{defn.line_start})")
        
        if len(definitions) > 10:
            summary.append(f"  ... and {len(definitions) - 10} more")
        summary.append("")
    
    return "\n".join(summary)
