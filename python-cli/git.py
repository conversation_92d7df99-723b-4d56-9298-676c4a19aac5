"""Git integration module for Cline CLI.

This module provides functionality for interacting with Git repositories.
"""
from typing import Dict, Any, List, Optional, Tuple
import os
import re
import subprocess
from pathlib import Path


def is_git_repository(path: str = ".") -> bool:
    """Check if a directory is a Git repository.
    
    Args:
        path: Path to check
        
    Returns:
        True if the directory is a Git repository, False otherwise
    """
    try:
        result = subprocess.run(
            ["git", "rev-parse", "--is-inside-work-tree"],
            cwd=path,
            check=False,
            capture_output=True,
            text=True,
        )
        
        return result.returncode == 0 and result.stdout.strip() == "true"
    except (subprocess.SubprocessError, FileNotFoundError):
        return False


def get_git_root(path: str = ".") -> Optional[str]:
    """Get the root directory of a Git repository.
    
    Args:
        path: Path to start from
        
    Returns:
        Root directory of the Git repository or None if not in a repository
    """
    if not is_git_repository(path):
        return None
        
    try:
        result = subprocess.run(
            ["git", "rev-parse", "--show-toplevel"],
            cwd=path,
            check=True,
            capture_output=True,
            text=True,
        )
        
        return result.stdout.strip()
    except (subprocess.SubprocessError, FileNotFoundError):
        return None


def get_current_branch(path: str = ".") -> Optional[str]:
    """Get the current Git branch.
    
    Args:
        path: Path to the Git repository
        
    Returns:
        Current branch name or None if not in a repository
    """
    if not is_git_repository(path):
        return None
        
    try:
        result = subprocess.run(
            ["git", "branch", "--show-current"],
            cwd=path,
            check=True,
            capture_output=True,
            text=True,
        )
        
        return result.stdout.strip()
    except (subprocess.SubprocessError, FileNotFoundError):
        return None


def get_modified_files(path: str = ".") -> List[str]:
    """Get a list of modified files in a Git repository.
    
    Args:
        path: Path to the Git repository
        
    Returns:
        List of modified file paths
    """
    if not is_git_repository(path):
        return []
        
    try:
        result = subprocess.run(
            ["git", "diff", "--name-only"],
            cwd=path,
            check=True,
            capture_output=True,
            text=True,
        )
        
        return [line.strip() for line in result.stdout.splitlines() if line.strip()]
    except (subprocess.SubprocessError, FileNotFoundError):
        return []


def get_untracked_files(path: str = ".") -> List[str]:
    """Get a list of untracked files in a Git repository.
    
    Args:
        path: Path to the Git repository
        
    Returns:
        List of untracked file paths
    """
    if not is_git_repository(path):
        return []
        
    try:
        result = subprocess.run(
            ["git", "ls-files", "--others", "--exclude-standard"],
            cwd=path,
            check=True,
            capture_output=True,
            text=True,
        )
        
        return [line.strip() for line in result.stdout.splitlines() if line.strip()]
    except (subprocess.SubprocessError, FileNotFoundError):
        return []


def get_staged_files(path: str = ".") -> List[str]:
    """Get a list of staged files in a Git repository.
    
    Args:
        path: Path to the Git repository
        
    Returns:
        List of staged file paths
    """
    if not is_git_repository(path):
        return []
        
    try:
        result = subprocess.run(
            ["git", "diff", "--name-only", "--staged"],
            cwd=path,
            check=True,
            capture_output=True,
            text=True,
        )
        
        return [line.strip() for line in result.stdout.splitlines() if line.strip()]
    except (subprocess.SubprocessError, FileNotFoundError):
        return []


def get_repository_status(path: str = ".") -> Dict[str, Any]:
    """Get the status of a Git repository.
    
    Args:
        path: Path to the Git repository
        
    Returns:
        Dictionary containing repository status information
    """
    if not is_git_repository(path):
        return {"is_git_repository": False}
        
    return {
        "is_git_repository": True,
        "root": get_git_root(path),
        "branch": get_current_branch(path),
        "modified_files": get_modified_files(path),
        "untracked_files": get_untracked_files(path),
        "staged_files": get_staged_files(path),
    }


def generate_commit_message(path: str = ".") -> Optional[str]:
    """Generate a commit message based on staged changes.
    
    Args:
        path: Path to the Git repository
        
    Returns:
        Generated commit message or None if not in a repository or no staged changes
    """
    if not is_git_repository(path):
        return None
        
    staged_files = get_staged_files(path)
    
    if not staged_files:
        return None
        
    try:
        # Get the diff of staged changes
        result = subprocess.run(
            ["git", "diff", "--staged"],
            cwd=path,
            check=True,
            capture_output=True,
            text=True,
        )
        
        diff = result.stdout
        
        if not diff:
            return None
            
        # Simple heuristic to generate a commit message
        if len(staged_files) == 1:
            file_name = staged_files[0]
            file_ext = os.path.splitext(file_name)[1]
            
            if file_ext in [".py", ".js", ".ts", ".java", ".c", ".cpp", ".h", ".hpp"]:
                return f"Update {file_name}"
            elif file_ext in [".md", ".txt", ".rst"]:
                return f"Update documentation in {file_name}"
            elif file_ext in [".json", ".yaml", ".yml", ".toml", ".ini", ".cfg"]:
                return f"Update configuration in {file_name}"
            else:
                return f"Update {file_name}"
        else:
            # Multiple files
            file_types = set(os.path.splitext(f)[1] for f in staged_files)
            
            if len(file_types) == 1:
                file_type = next(iter(file_types))
                if file_type in [".py", ".js", ".ts", ".java", ".c", ".cpp", ".h", ".hpp"]:
                    return f"Update {len(staged_files)} source files"
                elif file_type in [".md", ".txt", ".rst"]:
                    return f"Update {len(staged_files)} documentation files"
                elif file_type in [".json", ".yaml", ".yml", ".toml", ".ini", ".cfg"]:
                    return f"Update {len(staged_files)} configuration files"
                else:
                    return f"Update {len(staged_files)} files"
            else:
                return f"Update {len(staged_files)} files"
    except (subprocess.SubprocessError, FileNotFoundError):
        return None
