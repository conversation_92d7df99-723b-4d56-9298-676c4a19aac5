<?xml version="1.0" ?>
<coverage version="7.6.10" timestamp="1747471974777" lines-valid="349" lines-covered="74" line-rate="0.212" branches-covered="0" branches-valid="0" branch-rate="0" complexity="0">
	<!-- Generated by coverage.py: https://coverage.readthedocs.io/en/7.6.10 -->
	<!-- Based on https://raw.githubusercontent.com/cobertura/web/master/htdocs/xml/coverage-04.dtd -->
	<sources>
		<source>/Users/<USER>/Desktop/cline-main/python-cli/cline_cli</source>
	</sources>
	<packages>
		<package name="." line-rate="0.2595" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="3" hits="1"/>
					</lines>
				</class>
				<class name="cli.py" filename="cli.py" complexity="0" line-rate="0.2548" branch-rate="0">
					<methods/>
					<lines>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="26" hits="1"/>
						<line number="28" hits="0"/>
						<line number="29" hits="0"/>
						<line number="30" hits="0"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="40" hits="0"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="46" hits="0"/>
						<line number="47" hits="0"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="53" hits="0"/>
						<line number="55" hits="0"/>
						<line number="56" hits="0"/>
						<line number="57" hits="0"/>
						<line number="59" hits="0"/>
						<line number="60" hits="0"/>
						<line number="61" hits="0"/>
						<line number="62" hits="0"/>
						<line number="64" hits="0"/>
						<line number="65" hits="0"/>
						<line number="71" hits="0"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="82" hits="0"/>
						<line number="85" hits="1"/>
						<line number="86" hits="1"/>
						<line number="88" hits="0"/>
						<line number="89" hits="0"/>
						<line number="90" hits="0"/>
						<line number="91" hits="0"/>
						<line number="92" hits="0"/>
						<line number="93" hits="0"/>
						<line number="95" hits="0"/>
						<line number="96" hits="0"/>
						<line number="97" hits="0"/>
						<line number="98" hits="0"/>
						<line number="99" hits="0"/>
						<line number="102" hits="1"/>
						<line number="103" hits="1"/>
						<line number="105" hits="0"/>
						<line number="106" hits="0"/>
						<line number="108" hits="0"/>
						<line number="110" hits="0"/>
						<line number="111" hits="0"/>
						<line number="112" hits="0"/>
						<line number="114" hits="0"/>
						<line number="115" hits="0"/>
						<line number="117" hits="0"/>
						<line number="118" hits="0"/>
						<line number="121" hits="0"/>
						<line number="122" hits="0"/>
						<line number="123" hits="0"/>
						<line number="124" hits="0"/>
						<line number="125" hits="0"/>
						<line number="126" hits="0"/>
						<line number="127" hits="0"/>
						<line number="128" hits="0"/>
						<line number="129" hits="0"/>
						<line number="130" hits="0"/>
						<line number="131" hits="0"/>
						<line number="132" hits="0"/>
						<line number="135" hits="0"/>
						<line number="137" hits="0"/>
						<line number="138" hits="0"/>
						<line number="139" hits="0"/>
						<line number="140" hits="0"/>
						<line number="143" hits="1"/>
						<line number="144" hits="1"/>
						<line number="150" hits="0"/>
						<line number="151" hits="0"/>
						<line number="153" hits="0"/>
						<line number="156" hits="0"/>
						<line number="159" hits="0"/>
						<line number="160" hits="0"/>
						<line number="162" hits="0"/>
						<line number="165" hits="0"/>
						<line number="166" hits="0"/>
						<line number="167" hits="0"/>
						<line number="168" hits="0"/>
						<line number="171" hits="0"/>
						<line number="172" hits="0"/>
						<line number="173" hits="0"/>
						<line number="174" hits="0"/>
						<line number="177" hits="0"/>
						<line number="178" hits="0"/>
						<line number="179" hits="0"/>
						<line number="180" hits="0"/>
						<line number="184" hits="1"/>
						<line number="185" hits="1"/>
						<line number="188" hits="1"/>
						<line number="189" hits="1"/>
						<line number="191" hits="0"/>
						<line number="192" hits="0"/>
						<line number="194" hits="0"/>
						<line number="195" hits="0"/>
						<line number="196" hits="0"/>
						<line number="198" hits="0"/>
						<line number="199" hits="0"/>
						<line number="200" hits="0"/>
						<line number="203" hits="1"/>
						<line number="204" hits="1"/>
						<line number="206" hits="0"/>
						<line number="207" hits="0"/>
						<line number="209" hits="0"/>
						<line number="210" hits="0"/>
						<line number="211" hits="0"/>
						<line number="213" hits="0"/>
						<line number="214" hits="0"/>
						<line number="217" hits="1"/>
						<line number="218" hits="1"/>
						<line number="224" hits="0"/>
						<line number="225" hits="0"/>
						<line number="226" hits="0"/>
						<line number="228" hits="0"/>
						<line number="230" hits="0"/>
						<line number="231" hits="0"/>
						<line number="232" hits="0"/>
						<line number="233" hits="0"/>
						<line number="234" hits="0"/>
						<line number="235" hits="0"/>
						<line number="236" hits="0"/>
						<line number="238" hits="0"/>
						<line number="240" hits="0"/>
						<line number="242" hits="0"/>
						<line number="243" hits="0"/>
						<line number="245" hits="0"/>
						<line number="248" hits="1"/>
						<line number="249" hits="1"/>
						<line number="251" hits="0"/>
						<line number="252" hits="0"/>
						<line number="254" hits="0"/>
						<line number="255" hits="0"/>
						<line number="257" hits="0"/>
						<line number="260" hits="1"/>
						<line number="261" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="core" line-rate="0.3333" branch-rate="0" complexity="0">
			<classes>
				<class name="context.py" filename="core/context.py" complexity="0" line-rate="0.3333" branch-rate="0">
					<methods/>
					<lines>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="0"/>
						<line number="12" hits="0"/>
						<line number="13" hits="0"/>
						<line number="15" hits="1"/>
						<line number="17" hits="0"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0"/>
						<line number="26" hits="0"/>
						<line number="27" hits="0"/>
						<line number="29" hits="1"/>
						<line number="31" hits="0"/>
						<line number="32" hits="0"/>
						<line number="33" hits="0"/>
						<line number="34" hits="0"/>
						<line number="35" hits="0"/>
						<line number="36" hits="0"/>
						<line number="38" hits="1"/>
						<line number="40" hits="0"/>
						<line number="41" hits="0"/>
						<line number="43" hits="1"/>
						<line number="45" hits="0"/>
						<line number="51" hits="1"/>
						<line number="53" hits="0"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="migrations" line-rate="0.5" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="migrations/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="js_to_python.py" filename="migrations/js_to_python.py" complexity="0" line-rate="0.5" branch-rate="0">
					<methods/>
					<lines>
						<line number="3" hits="1"/>
						<line number="9" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="prompts" line-rate="0.2245" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="prompts/__init__.py" complexity="0" line-rate="0.2245" branch-rate="0">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="12" hits="1"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="22" hits="0"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0"/>
						<line number="27" hits="1"/>
						<line number="29" hits="0"/>
						<line number="30" hits="0"/>
						<line number="32" hits="0"/>
						<line number="33" hits="0"/>
						<line number="34" hits="0"/>
						<line number="35" hits="0"/>
						<line number="38" hits="0"/>
						<line number="39" hits="0"/>
						<line number="40" hits="0"/>
						<line number="41" hits="0"/>
						<line number="43" hits="1"/>
						<line number="52" hits="0"/>
						<line number="54" hits="1"/>
						<line number="60" hits="0"/>
						<line number="62" hits="1"/>
						<line number="72" hits="0"/>
						<line number="74" hits="0"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="84" hits="0"/>
						<line number="85" hits="0"/>
						<line number="87" hits="1"/>
						<line number="96" hits="0"/>
						<line number="97" hits="0"/>
						<line number="99" hits="0"/>
						<line number="100" hits="0"/>
						<line number="101" hits="0"/>
						<line number="102" hits="0"/>
						<line number="105" hits="0"/>
						<line number="106" hits="0"/>
						<line number="107" hits="0"/>
						<line number="108" hits="0"/>
						<line number="110" hits="1"/>
						<line number="121" hits="0"/>
						<line number="124" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="tools" line-rate="0.2045" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="tools/__init__.py" complexity="0" line-rate="0.2045" branch-rate="0">
					<methods/>
					<lines>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="17" hits="0"/>
						<line number="18" hits="0"/>
						<line number="26" hits="0"/>
						<line number="27" hits="0"/>
						<line number="28" hits="0"/>
						<line number="31" hits="1"/>
						<line number="46" hits="0"/>
						<line number="47" hits="0"/>
						<line number="49" hits="0"/>
						<line number="50" hits="0"/>
						<line number="52" hits="0"/>
						<line number="53" hits="0"/>
						<line number="54" hits="0"/>
						<line number="55" hits="0"/>
						<line number="56" hits="0"/>
						<line number="57" hits="0"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="61" hits="0"/>
						<line number="64" hits="1"/>
						<line number="73" hits="0"/>
						<line number="74" hits="0"/>
						<line number="75" hits="0"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="81" hits="0"/>
						<line number="84" hits="1"/>
						<line number="91" hits="0"/>
						<line number="92" hits="0"/>
						<line number="94" hits="0"/>
						<line number="95" hits="0"/>
						<line number="96" hits="0"/>
						<line number="97" hits="0"/>
						<line number="100" hits="1"/>
						<line number="110" hits="0"/>
						<line number="111" hits="0"/>
						<line number="112" hits="0"/>
						<line number="114" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="utils" line-rate="0" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="utils/__init__.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="2" hits="0"/>
						<line number="3" hits="0"/>
						<line number="4" hits="0"/>
						<line number="5" hits="0"/>
						<line number="6" hits="0"/>
						<line number="8" hits="0"/>
						<line number="10" hits="0"/>
						<line number="11" hits="0"/>
						<line number="12" hits="0"/>
						<line number="15" hits="0"/>
						<line number="17" hits="0"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="24" hits="0"/>
						<line number="26" hits="0"/>
						<line number="27" hits="0"/>
						<line number="28" hits="0"/>
						<line number="29" hits="0"/>
						<line number="30" hits="0"/>
						<line number="31" hits="0"/>
						<line number="34" hits="0"/>
						<line number="36" hits="0"/>
						<line number="39" hits="0"/>
						<line number="41" hits="0"/>
						<line number="42" hits="0"/>
						<line number="43" hits="0"/>
						<line number="44" hits="0"/>
						<line number="45" hits="0"/>
						<line number="46" hits="0"/>
						<line number="47" hits="0"/>
						<line number="48" hits="0"/>
						<line number="50" hits="0"/>
						<line number="51" hits="0"/>
						<line number="52" hits="0"/>
						<line number="53" hits="0"/>
						<line number="54" hits="0"/>
						<line number="55" hits="0"/>
						<line number="58" hits="0"/>
						<line number="60" hits="0"/>
						<line number="61" hits="0"/>
						<line number="62" hits="0"/>
						<line number="63" hits="0"/>
						<line number="64" hits="0"/>
						<line number="67" hits="0"/>
						<line number="69" hits="0"/>
						<line number="70" hits="0"/>
						<line number="72" hits="0"/>
						<line number="73" hits="0"/>
						<line number="74" hits="0"/>
						<line number="75" hits="0"/>
						<line number="76" hits="0"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
	</packages>
</coverage>
