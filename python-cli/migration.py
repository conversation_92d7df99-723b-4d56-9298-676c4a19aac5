"""Migration utilities for converting from JavaScript Cline to Python CLI.

This module provides functions to migrate settings, tasks, and other data
from the JavaScript/TypeScript Cline extension to the Python CLI version.
"""
import json
import os
import shutil
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import logging

from api import ApiConfiguration, ApiProvider
from task import Task


class MigrationError(Exception):
    """Exception raised during migration process."""
    pass


class ClineMigrator:
    """Handles migration from JavaScript Cline to Python CLI."""
    
    def __init__(self, source_dir: str, target_dir: str):
        """Initialize the migrator.
        
        Args:
            source_dir: Path to the JavaScript Cline project
            target_dir: Path to the Python CLI project
        """
        self.source_dir = Path(source_dir)
        self.target_dir = Path(target_dir)
        self.logger = logging.getLogger(__name__)
        
        # Ensure target directory exists
        self.target_dir.mkdir(parents=True, exist_ok=True)
    
    def migrate_all(self) -> Dict[str, Any]:
        """Migrate all components from JavaScript to Python.
        
        Returns:
            Dictionary containing migration results
        """
        results = {
            "migrated_components": [],
            "failed_components": [],
            "success": True,
            "warnings": []
        }
        
        try:
            # Migrate VS Code settings
            settings_result = self.migrate_vscode_settings()
            if settings_result["success"]:
                results["migrated_components"].append("vscode_settings")
            else:
                results["failed_components"].append("vscode_settings")
                results["warnings"].extend(settings_result.get("warnings", []))
            
            # Migrate task history
            tasks_result = self.migrate_task_history()
            if tasks_result["success"]:
                results["migrated_components"].append("task_history")
            else:
                results["failed_components"].append("task_history")
                results["warnings"].extend(tasks_result.get("warnings", []))
            
            # Migrate custom instructions
            instructions_result = self.migrate_custom_instructions()
            if instructions_result["success"]:
                results["migrated_components"].append("custom_instructions")
            else:
                results["failed_components"].append("custom_instructions")
                results["warnings"].extend(instructions_result.get("warnings", []))
            
            # Migrate prompts
            prompts_result = self.migrate_prompts()
            if prompts_result["success"]:
                results["migrated_components"].append("prompts")
            else:
                results["failed_components"].append("prompts")
                results["warnings"].extend(prompts_result.get("warnings", []))
            
        except Exception as e:
            results["success"] = False
            results["error"] = str(e)
            self.logger.error(f"Migration failed: {e}")
        
        # Overall success if no critical failures
        if results["failed_components"]:
            results["success"] = False
        
        return results
    
    def migrate_vscode_settings(self) -> Dict[str, Any]:
        """Migrate VS Code settings to Python CLI configuration.
        
        Returns:
            Migration result dictionary
        """
        result = {"success": False, "warnings": []}
        
        try:
            # Look for VS Code settings
            vscode_settings_path = self.source_dir / ".vscode" / "settings.json"
            
            if not vscode_settings_path.exists():
                result["warnings"].append("No VS Code settings found")
                result["success"] = True  # Not an error, just no settings to migrate
                return result
            
            with open(vscode_settings_path, 'r', encoding='utf-8') as f:
                vscode_settings = json.load(f)
            
            # Extract Cline-specific settings
            python_config = {}
            
            # Map common settings
            setting_mappings = {
                "cline.apiKey": "api_key",
                "cline.model": "model",
                "cline.endpoint": "endpoint",
                "cline.temperature": "temperature",
                "cline.maxTokens": "max_tokens",
                "cline.customInstructions": "custom_instructions",
                "cline.autoApproval": "auto_approval",
                "cline.telemetry": "telemetry_enabled"
            }
            
            for vscode_key, python_key in setting_mappings.items():
                if vscode_key in vscode_settings:
                    python_config[python_key] = vscode_settings[vscode_key]
            
            # Determine provider from model or endpoint
            if "model" in python_config:
                model = python_config["model"]
                if model.startswith("gpt-") or model.startswith("o1-"):
                    python_config["provider"] = ApiProvider.OPENAI.value
                elif model.startswith("claude-"):
                    python_config["provider"] = ApiProvider.ANTHROPIC.value
                elif "gemini" in model.lower():
                    python_config["provider"] = ApiProvider.GEMINI.value
                else:
                    python_config["provider"] = ApiProvider.OPENAI.value  # Default
            
            # Save Python configuration
            config_path = self.target_dir / "config.json"
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(python_config, f, indent=2)
            
            result["success"] = True
            result["migrated_settings"] = len(python_config)
            
        except Exception as e:
            result["error"] = str(e)
            self.logger.error(f"Failed to migrate VS Code settings: {e}")
        
        return result
    
    def migrate_task_history(self) -> Dict[str, Any]:
        """Migrate task history from JavaScript to Python format.
        
        Returns:
            Migration result dictionary
        """
        result = {"success": False, "warnings": []}
        
        try:
            # Look for task history in common locations
            possible_paths = [
                self.source_dir / "tasks",
                self.source_dir / ".cline" / "tasks",
                self.source_dir / "node_modules" / ".cline" / "tasks"
            ]
            
            tasks_dir = None
            for path in possible_paths:
                if path.exists() and path.is_dir():
                    tasks_dir = path
                    break
            
            if not tasks_dir:
                result["warnings"].append("No task history found")
                result["success"] = True
                return result
            
            # Create target tasks directory
            target_tasks_dir = self.target_dir / "tasks"
            target_tasks_dir.mkdir(exist_ok=True)
            
            migrated_count = 0
            
            # Migrate each task
            for task_file in tasks_dir.glob("*.json"):
                try:
                    with open(task_file, 'r', encoding='utf-8') as f:
                        js_task = json.load(f)
                    
                    # Convert to Python task format
                    python_task = self._convert_task_format(js_task)
                    
                    # Save converted task
                    target_file = target_tasks_dir / task_file.name
                    with open(target_file, 'w', encoding='utf-8') as f:
                        json.dump(python_task, f, indent=2)
                    
                    migrated_count += 1
                    
                except Exception as e:
                    result["warnings"].append(f"Failed to migrate task {task_file.name}: {e}")
            
            result["success"] = True
            result["migrated_tasks"] = migrated_count
            
        except Exception as e:
            result["error"] = str(e)
            self.logger.error(f"Failed to migrate task history: {e}")
        
        return result
    
    def migrate_custom_instructions(self) -> Dict[str, Any]:
        """Migrate custom instructions.
        
        Returns:
            Migration result dictionary
        """
        result = {"success": False, "warnings": []}
        
        try:
            # Look for custom instructions
            instructions_paths = [
                self.source_dir / "custom_instructions.txt",
                self.source_dir / ".cline" / "instructions.txt",
                self.source_dir / "instructions.md"
            ]
            
            instructions_content = ""
            found_instructions = False
            
            for path in instructions_paths:
                if path.exists():
                    with open(path, 'r', encoding='utf-8') as f:
                        instructions_content += f.read() + "\n\n"
                    found_instructions = True
            
            if found_instructions:
                # Save to target
                target_path = self.target_dir / "custom_instructions.txt"
                with open(target_path, 'w', encoding='utf-8') as f:
                    f.write(instructions_content.strip())
                
                result["success"] = True
            else:
                result["warnings"].append("No custom instructions found")
                result["success"] = True
            
        except Exception as e:
            result["error"] = str(e)
            self.logger.error(f"Failed to migrate custom instructions: {e}")
        
        return result
    
    def migrate_prompts(self) -> Dict[str, Any]:
        """Migrate prompt templates.
        
        Returns:
            Migration result dictionary
        """
        result = {"success": False, "warnings": []}
        
        try:
            # Look for prompts directory
            prompts_paths = [
                self.source_dir / "src" / "core" / "prompts",
                self.source_dir / "prompts",
                self.source_dir / ".cline" / "prompts"
            ]
            
            prompts_dir = None
            for path in prompts_paths:
                if path.exists() and path.is_dir():
                    prompts_dir = path
                    break
            
            if not prompts_dir:
                result["warnings"].append("No prompts directory found")
                result["success"] = True
                return result
            
            # Create target prompts directory
            target_prompts_dir = self.target_dir / "prompts"
            target_prompts_dir.mkdir(exist_ok=True)
            
            # Copy prompt files
            migrated_count = 0
            for prompt_file in prompts_dir.rglob("*"):
                if prompt_file.is_file() and prompt_file.suffix in ['.txt', '.md', '.json']:
                    target_file = target_prompts_dir / prompt_file.name
                    shutil.copy2(prompt_file, target_file)
                    migrated_count += 1
            
            result["success"] = True
            result["migrated_prompts"] = migrated_count
            
        except Exception as e:
            result["error"] = str(e)
            self.logger.error(f"Failed to migrate prompts: {e}")
        
        return result
    
    def _convert_task_format(self, js_task: Dict[str, Any]) -> Dict[str, Any]:
        """Convert JavaScript task format to Python format.
        
        Args:
            js_task: JavaScript task data
            
        Returns:
            Python task data
        """
        # Basic conversion - adapt as needed based on actual JS task structure
        python_task = {
            "id": js_task.get("id", ""),
            "title": js_task.get("title", "Migrated Task"),
            "created_at": js_task.get("createdAt", js_task.get("created_at", "")),
            "updated_at": js_task.get("updatedAt", js_task.get("updated_at", "")),
            "messages": [],
            "metadata": js_task.get("metadata", {})
        }
        
        # Convert messages if they exist
        if "messages" in js_task:
            for msg in js_task["messages"]:
                python_msg = {
                    "id": msg.get("id", ""),
                    "role": msg.get("role", "user"),
                    "content": msg.get("content", ""),
                    "created_at": msg.get("createdAt", msg.get("created_at", "")),
                    "metadata": msg.get("metadata", {})
                }
                python_task["messages"].append(python_msg)
        
        return python_task


def run_migration(source_dir: str, target_dir: str) -> Dict[str, Any]:
    """Run the complete migration process.
    
    Args:
        source_dir: Path to the JavaScript Cline project
        target_dir: Path to the Python CLI project
        
    Returns:
        Migration results
    """
    migrator = ClineMigrator(source_dir, target_dir)
    return migrator.migrate_all()
