"""File system utilities for Cline CLI.

This module provides utility functions for working with the file system.
"""
from pathlib import Path
import os
import shutil
from typing import List, Optional, Union


def file_exists(path: Union[str, Path]) -> bool:
    """Check if a file exists.
    
    Args:
        path: Path to the file
        
    Returns:
        True if the file exists, False otherwise
    """
    return Path(path).exists()


def ensure_directory_exists(directory: Union[str, Path]) -> Path:
    """Ensure a directory exists, creating it if necessary.
    
    Args:
        directory: Path to the directory
        
    Returns:
        Path object for the directory
    """
    path = Path(directory)
    path.mkdir(parents=True, exist_ok=True)
    return path


def get_file_size(path: Union[str, Path]) -> int:
    """Get the size of a file in bytes.
    
    Args:
        path: Path to the file
        
    Returns:
        Size of the file in bytes
    """
    return Path(path).stat().st_size


def list_files(directory: Union[str, Path], pattern: str = "*") -> List[Path]:
    """List files in a directory matching a pattern.
    
    Args:
        directory: Path to the directory
        pattern: Glob pattern to match
        
    Returns:
        List of matching file paths
    """
    return list(Path(directory).glob(pattern))


def list_files_recursive(directory: Union[str, Path], pattern: str = "*") -> List[Path]:
    """List files recursively in a directory matching a pattern.
    
    Args:
        directory: Path to the directory
        pattern: Glob pattern to match
        
    Returns:
        List of matching file paths
    """
    return list(Path(directory).rglob(pattern))


def read_file(path: Union[str, Path], encoding: str = "utf-8") -> str:
    """Read a file and return its contents.
    
    Args:
        path: Path to the file
        encoding: File encoding
        
    Returns:
        File contents as string
    """
    return Path(path).read_text(encoding=encoding)


def write_file(path: Union[str, Path], content: str, encoding: str = "utf-8") -> None:
    """Write content to a file.
    
    Args:
        path: Path to the file
        content: Content to write
        encoding: File encoding
    """
    # Ensure the parent directory exists
    Path(path).parent.mkdir(parents=True, exist_ok=True)
    Path(path).write_text(content, encoding=encoding)


def copy_file(source: Union[str, Path], destination: Union[str, Path]) -> None:
    """Copy a file from source to destination.
    
    Args:
        source: Path to the source file
        destination: Path to the destination file
    """
    # Ensure the parent directory exists
    Path(destination).parent.mkdir(parents=True, exist_ok=True)
    shutil.copy2(source, destination)


def move_file(source: Union[str, Path], destination: Union[str, Path]) -> None:
    """Move a file from source to destination.
    
    Args:
        source: Path to the source file
        destination: Path to the destination file
    """
    # Ensure the parent directory exists
    Path(destination).parent.mkdir(parents=True, exist_ok=True)
    shutil.move(source, destination)


def delete_file(path: Union[str, Path]) -> None:
    """Delete a file.
    
    Args:
        path: Path to the file
    """
    Path(path).unlink(missing_ok=True)


def get_file_extension(path: Union[str, Path]) -> str:
    """Get the extension of a file.
    
    Args:
        path: Path to the file
        
    Returns:
        File extension (without the dot)
    """
    return Path(path).suffix.lstrip(".")


def get_file_name(path: Union[str, Path]) -> str:
    """Get the name of a file without the extension.
    
    Args:
        path: Path to the file
        
    Returns:
        File name without extension
    """
    return Path(path).stem


def get_file_path(path: Union[str, Path]) -> str:
    """Get the absolute path of a file.
    
    Args:
        path: Path to the file
        
    Returns:
        Absolute path as string
    """
    return str(Path(path).resolve())


def get_home_directory() -> Path:
    """Get the user's home directory.
    
    Returns:
        Path to the home directory
    """
    return Path.home()


def get_config_directory() -> Path:
    """Get the Cline configuration directory.
    
    Returns:
        Path to the configuration directory
    """
    return Path.home() / ".cline"
