"""URL content fetching utilities for Cline CLI.

This module provides functionality to fetch and extract content from URLs,
similar to the UrlContentFetcher in the JavaScript version.
"""
import asyncio
import re
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin, urlparse
import aiohttp
from dataclasses import dataclass


@dataclass
class UrlContent:
    """Represents content fetched from a URL."""
    url: str
    title: Optional[str] = None
    content: Optional[str] = None
    error: Optional[str] = None
    status_code: Optional[int] = None
    content_type: Optional[str] = None


class UrlContentFetcher:
    """Fetches and extracts content from URLs."""
    
    def __init__(self, timeout: int = 30, max_content_length: int = 1024 * 1024):
        """Initialize the URL content fetcher.
        
        Args:
            timeout: Request timeout in seconds
            max_content_length: Maximum content length to fetch (1MB default)
        """
        self.timeout = timeout
        self.max_content_length = max_content_length
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout),
            headers={
                'User-Agent': 'Cline-CLI/1.0.0 (https://github.com/cline/cline)'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def fetch_url_content(self, url: str) -> UrlContent:
        """Fetch content from a URL.
        
        Args:
            url: URL to fetch content from
            
        Returns:
            UrlContent object with fetched data
        """
        if not self.session:
            raise RuntimeError("Session not initialized. Use async context manager.")
        
        try:
            # Validate URL
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                return UrlContent(
                    url=url,
                    error="Invalid URL format"
                )
            
            # Fetch the content
            async with self.session.get(url) as response:
                status_code = response.status
                content_type = response.headers.get('content-type', '').lower()
                
                # Check content length
                content_length = response.headers.get('content-length')
                if content_length and int(content_length) > self.max_content_length:
                    return UrlContent(
                        url=url,
                        status_code=status_code,
                        content_type=content_type,
                        error=f"Content too large: {content_length} bytes"
                    )
                
                # Read content with size limit
                content_bytes = await response.content.read(self.max_content_length)
                
                # Decode content
                try:
                    content = content_bytes.decode('utf-8', errors='ignore')
                except UnicodeDecodeError:
                    content = content_bytes.decode('latin-1', errors='ignore')
                
                # Extract title and clean content based on content type
                if 'text/html' in content_type:
                    title, cleaned_content = self._extract_html_content(content)
                elif 'text/plain' in content_type:
                    title = self._extract_title_from_url(url)
                    cleaned_content = content
                elif 'application/json' in content_type:
                    title = self._extract_title_from_url(url)
                    cleaned_content = self._format_json_content(content)
                elif 'text/markdown' in content_type or url.endswith('.md'):
                    title = self._extract_markdown_title(content)
                    cleaned_content = content
                else:
                    title = self._extract_title_from_url(url)
                    cleaned_content = content[:5000]  # Limit non-text content
                
                return UrlContent(
                    url=url,
                    title=title,
                    content=cleaned_content,
                    status_code=status_code,
                    content_type=content_type
                )
        
        except asyncio.TimeoutError:
            return UrlContent(
                url=url,
                error="Request timeout"
            )
        except aiohttp.ClientError as e:
            return UrlContent(
                url=url,
                error=f"Network error: {str(e)}"
            )
        except Exception as e:
            return UrlContent(
                url=url,
                error=f"Unexpected error: {str(e)}"
            )
    
    def _extract_html_content(self, html: str) -> tuple[Optional[str], str]:
        """Extract title and clean content from HTML.
        
        Args:
            html: HTML content
            
        Returns:
            Tuple of (title, cleaned_content)
        """
        # Extract title
        title_match = re.search(r'<title[^>]*>(.*?)</title>', html, re.IGNORECASE | re.DOTALL)
        title = title_match.group(1).strip() if title_match else None
        
        # Remove script and style tags
        html = re.sub(r'<script[^>]*>.*?</script>', '', html, flags=re.IGNORECASE | re.DOTALL)
        html = re.sub(r'<style[^>]*>.*?</style>', '', html, flags=re.IGNORECASE | re.DOTALL)
        
        # Remove HTML tags
        text = re.sub(r'<[^>]+>', '', html)
        
        # Clean up whitespace
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        # Limit length
        if len(text) > 10000:
            text = text[:10000] + "... [content truncated]"
        
        return title, text
    
    def _extract_title_from_url(self, url: str) -> str:
        """Extract a title from the URL path.
        
        Args:
            url: URL to extract title from
            
        Returns:
            Extracted title
        """
        parsed = urlparse(url)
        path = parsed.path.strip('/')
        
        if path:
            # Use the last part of the path
            title = path.split('/')[-1]
            # Remove file extension
            title = re.sub(r'\.[^.]+$', '', title)
            # Replace hyphens and underscores with spaces
            title = re.sub(r'[-_]', ' ', title)
            return title.title()
        
        return parsed.netloc
    
    def _extract_markdown_title(self, content: str) -> Optional[str]:
        """Extract title from Markdown content.
        
        Args:
            content: Markdown content
            
        Returns:
            Extracted title or None
        """
        # Look for first H1 heading
        h1_match = re.search(r'^#\s+(.+)$', content, re.MULTILINE)
        if h1_match:
            return h1_match.group(1).strip()
        
        # Look for any heading
        heading_match = re.search(r'^#+\s+(.+)$', content, re.MULTILINE)
        if heading_match:
            return heading_match.group(1).strip()
        
        return None
    
    def _format_json_content(self, content: str) -> str:
        """Format JSON content for better readability.
        
        Args:
            content: JSON content
            
        Returns:
            Formatted JSON content
        """
        try:
            import json
            data = json.loads(content)
            return json.dumps(data, indent=2, ensure_ascii=False)
        except json.JSONDecodeError:
            return content
    
    async def fetch_multiple_urls(self, urls: List[str]) -> List[UrlContent]:
        """Fetch content from multiple URLs concurrently.
        
        Args:
            urls: List of URLs to fetch
            
        Returns:
            List of UrlContent objects
        """
        if not self.session:
            raise RuntimeError("Session not initialized. Use async context manager.")
        
        tasks = [self.fetch_url_content(url) for url in urls]
        return await asyncio.gather(*tasks, return_exceptions=False)
    
    async def search_and_fetch(self, query: str, search_engine: str = "duckduckgo") -> List[UrlContent]:
        """Search for content and fetch results.
        
        Args:
            query: Search query
            search_engine: Search engine to use
            
        Returns:
            List of UrlContent objects from search results
        """
        # This is a simplified implementation
        # In a real implementation, you would integrate with search APIs
        
        if search_engine == "duckduckgo":
            search_url = f"https://duckduckgo.com/html/?q={query}"
        elif search_engine == "google":
            search_url = f"https://www.google.com/search?q={query}"
        else:
            return []
        
        # Fetch search results page
        search_result = await self.fetch_url_content(search_url)
        
        if search_result.error or not search_result.content:
            return []
        
        # Extract URLs from search results (simplified)
        urls = self._extract_urls_from_search_results(search_result.content)
        
        # Fetch content from found URLs (limit to first 5)
        if urls:
            return await self.fetch_multiple_urls(urls[:5])
        
        return []
    
    def _extract_urls_from_search_results(self, html: str) -> List[str]:
        """Extract URLs from search results HTML.
        
        Args:
            html: Search results HTML
            
        Returns:
            List of extracted URLs
        """
        # This is a simplified implementation
        # Real implementation would need to handle different search engines
        
        urls = []
        
        # Look for common URL patterns in search results
        url_patterns = [
            r'href="(https?://[^"]+)"',
            r"href='(https?://[^']+)'",
        ]
        
        for pattern in url_patterns:
            matches = re.findall(pattern, html)
            for match in matches:
                # Filter out search engine URLs and common non-content URLs
                if not any(domain in match for domain in [
                    'google.com', 'duckduckgo.com', 'bing.com',
                    'facebook.com', 'twitter.com', 'youtube.com'
                ]):
                    urls.append(match)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_urls = []
        for url in urls:
            if url not in seen:
                seen.add(url)
                unique_urls.append(url)
        
        return unique_urls


async def fetch_url_content(url: str) -> UrlContent:
    """Convenience function to fetch content from a single URL.
    
    Args:
        url: URL to fetch content from
        
    Returns:
        UrlContent object with fetched data
    """
    async with UrlContentFetcher() as fetcher:
        return await fetcher.fetch_url_content(url)


async def fetch_multiple_urls(urls: List[str]) -> List[UrlContent]:
    """Convenience function to fetch content from multiple URLs.
    
    Args:
        urls: List of URLs to fetch
        
    Returns:
        List of UrlContent objects
    """
    async with UrlContentFetcher() as fetcher:
        return await fetcher.fetch_multiple_urls(urls)
