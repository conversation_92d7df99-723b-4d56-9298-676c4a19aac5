// GENERATED CODE -- DO NOT EDIT!
// Generated by generate-server-setup.mjs
import * as grpc from "@grpc/grpc-js"
import { Controller } from "../core/controller"
import { Grp<PERSON><PERSON><PERSON>lerWrapper, GrpcStreamingResponseHandlerWrapper } from "./grpc-types"

// Account Service
import { accountLoginClicked } from "../core/controller/account/accountLoginClicked"

// Browser Service
import { getBrowserConnectionInfo } from "../core/controller/browser/getBrowserConnectionInfo"
import { testBrowserConnection } from "../core/controller/browser/testBrowserConnection"
import { discoverBrowser } from "../core/controller/browser/discoverBrowser"
import { getDetectedChromePath } from "../core/controller/browser/getDetectedChromePath"
import { updateBrowserSettings } from "../core/controller/browser/updateBrowserSettings"

// Checkpoints Service
import { checkpointDiff } from "../core/controller/checkpoints/checkpointDiff"
import { checkpointRestore } from "../core/controller/checkpoints/checkpointRestore"

// File Service
import { openFile } from "../core/controller/file/openFile"
import { selectImages } from "../core/controller/file/selectImages"
import { openImage } from "../core/controller/file/openImage"
import { deleteRuleFile } from "../core/controller/file/deleteRuleFile"
import { createRuleFile } from "../core/controller/file/createRuleFile"
import { searchCommits } from "../core/controller/file/searchCommits"
import { getRelativePaths } from "../core/controller/file/getRelativePaths"
import { searchFiles } from "../core/controller/file/searchFiles"

// Mcp Service
import { toggleMcpServer } from "../core/controller/mcp/toggleMcpServer"
import { updateMcpTimeout } from "../core/controller/mcp/updateMcpTimeout"
import { addRemoteMcpServer } from "../core/controller/mcp/addRemoteMcpServer"
import { downloadMcp } from "../core/controller/mcp/downloadMcp"

// Models Service
import { getOllamaModels } from "../core/controller/models/getOllamaModels"
import { getLmStudioModels } from "../core/controller/models/getLmStudioModels"
import { getVsCodeLmModels } from "../core/controller/models/getVsCodeLmModels"
import { refreshOpenRouterModels } from "../core/controller/models/refreshOpenRouterModels"
import { refreshOpenAiModels } from "../core/controller/models/refreshOpenAiModels"
import { refreshRequestyModels } from "../core/controller/models/refreshRequestyModels"

// Slash Service
import { reportBug } from "../core/controller/slash/reportBug"
import { condense } from "../core/controller/slash/condense"

// State Service
import { getLatestState } from "../core/controller/state/getLatestState"
import { subscribeToState } from "../core/controller/state/subscribeToState"
import { toggleFavoriteModel } from "../core/controller/state/toggleFavoriteModel"

// Task Service
import { cancelTask } from "../core/controller/task/cancelTask"
import { clearTask } from "../core/controller/task/clearTask"
import { deleteTasksWithIds } from "../core/controller/task/deleteTasksWithIds"
import { newTask } from "../core/controller/task/newTask"
import { showTaskWithId } from "../core/controller/task/showTaskWithId"
import { exportTaskWithId } from "../core/controller/task/exportTaskWithId"
import { toggleTaskFavorite } from "../core/controller/task/toggleTaskFavorite"
import { deleteNonFavoritedTasks } from "../core/controller/task/deleteNonFavoritedTasks"
import { getTaskHistory } from "../core/controller/task/getTaskHistory"
import { askResponse } from "../core/controller/task/askResponse"

// Web Service
import { checkIsImageUrl } from "../core/controller/web/checkIsImageUrl"

export function addServices(
	server: grpc.Server,
	proto: any,
	controller: Controller,
	wrapper: GrpcHandlerWrapper,
	wrapStreamingResponse: GrpcStreamingResponseHandlerWrapper,
): void {
	// Account Service
	server.addService(proto.cline.AccountService.service, {
		accountLoginClicked: wrapper(accountLoginClicked, controller),
	})

	// Browser Service
	server.addService(proto.cline.BrowserService.service, {
		getBrowserConnectionInfo: wrapper(getBrowserConnectionInfo, controller),
		testBrowserConnection: wrapper(testBrowserConnection, controller),
		discoverBrowser: wrapper(discoverBrowser, controller),
		getDetectedChromePath: wrapper(getDetectedChromePath, controller),
		updateBrowserSettings: wrapper(updateBrowserSettings, controller),
	})

	// Checkpoints Service
	server.addService(proto.cline.CheckpointsService.service, {
		checkpointDiff: wrapper(checkpointDiff, controller),
		checkpointRestore: wrapper(checkpointRestore, controller),
	})

	// File Service
	server.addService(proto.cline.FileService.service, {
		openFile: wrapper(openFile, controller),
		selectImages: wrapper(selectImages, controller),
		openImage: wrapper(openImage, controller),
		deleteRuleFile: wrapper(deleteRuleFile, controller),
		createRuleFile: wrapper(createRuleFile, controller),
		searchCommits: wrapper(searchCommits, controller),
		getRelativePaths: wrapper(getRelativePaths, controller),
		searchFiles: wrapper(searchFiles, controller),
	})

	// Mcp Service
	server.addService(proto.cline.McpService.service, {
		toggleMcpServer: wrapper(toggleMcpServer, controller),
		updateMcpTimeout: wrapper(updateMcpTimeout, controller),
		addRemoteMcpServer: wrapper(addRemoteMcpServer, controller),
		downloadMcp: wrapper(downloadMcp, controller),
	})

	// Models Service
	server.addService(proto.cline.ModelsService.service, {
		getOllamaModels: wrapper(getOllamaModels, controller),
		getLmStudioModels: wrapper(getLmStudioModels, controller),
		getVsCodeLmModels: wrapper(getVsCodeLmModels, controller),
		refreshOpenRouterModels: wrapper(refreshOpenRouterModels, controller),
		refreshOpenAiModels: wrapper(refreshOpenAiModels, controller),
		refreshRequestyModels: wrapper(refreshRequestyModels, controller),
	})

	// Slash Service
	server.addService(proto.cline.SlashService.service, {
		reportBug: wrapper(reportBug, controller),
		condense: wrapper(condense, controller),
	})

	// State Service
	server.addService(proto.cline.StateService.service, {
		getLatestState: wrapper(getLatestState, controller),
		subscribeToState: wrapStreamingResponse(subscribeToState, controller),
		toggleFavoriteModel: wrapper(toggleFavoriteModel, controller),
	})

	// Task Service
	server.addService(proto.cline.TaskService.service, {
		cancelTask: wrapper(cancelTask, controller),
		clearTask: wrapper(clearTask, controller),
		deleteTasksWithIds: wrapper(deleteTasksWithIds, controller),
		newTask: wrapper(newTask, controller),
		showTaskWithId: wrapper(showTaskWithId, controller),
		exportTaskWithId: wrapper(exportTaskWithId, controller),
		toggleTaskFavorite: wrapper(toggleTaskFavorite, controller),
		deleteNonFavoritedTasks: wrapper(deleteNonFavoritedTasks, controller),
		getTaskHistory: wrapper(getTaskHistory, controller),
		askResponse: wrapper(askResponse, controller),
	})

	// Web Service
	server.addService(proto.cline.WebService.service, {
		checkIsImageUrl: wrapper(checkIsImageUrl, controller),
	})
}
