// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.19.1
// source: browser.proto

/* eslint-disable */
import { Binary<PERSON>eader, BinaryWriter } from "@bufbuild/protobuf/wire"
import { Boolean, EmptyRequest, Metadata, StringRequest } from "./common"

export const protobufPackage = "cline"

export interface BrowserConnectionInfo {
	isConnected: boolean
	isRemote: boolean
	host?: string | undefined
}

export interface BrowserConnection {
	success: boolean
	message: string
	endpoint?: string | undefined
}

export interface ChromePath {
	path: string
	isBundled: boolean
}

export interface Viewport {
	width: number
	height: number
}

export interface BrowserSettings {
	viewport?: Viewport | undefined
	remoteBrowserHost?: string | undefined
	remoteBrowserEnabled?: boolean | undefined
	chromeExecutablePath?: string | undefined
	disableToolUse?: boolean | undefined
}

export interface UpdateBrowserSettingsRequest {
	metadata?: Metadata | undefined
	viewport?: Viewport | undefined
	remoteBrowserHost?: string | undefined
	remoteBrowserEnabled?: boolean | undefined
	chromeExecutablePath?: string | undefined
	disableToolUse?: boolean | undefined
}

function createBaseBrowserConnectionInfo(): BrowserConnectionInfo {
	return { isConnected: false, isRemote: false, host: undefined }
}

export const BrowserConnectionInfo: MessageFns<BrowserConnectionInfo> = {
	encode(message: BrowserConnectionInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
		if (message.isConnected !== false) {
			writer.uint32(8).bool(message.isConnected)
		}
		if (message.isRemote !== false) {
			writer.uint32(16).bool(message.isRemote)
		}
		if (message.host !== undefined) {
			writer.uint32(26).string(message.host)
		}
		return writer
	},

	decode(input: BinaryReader | Uint8Array, length?: number): BrowserConnectionInfo {
		const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseBrowserConnectionInfo()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 8) {
						break
					}

					message.isConnected = reader.bool()
					continue
				}
				case 2: {
					if (tag !== 16) {
						break
					}

					message.isRemote = reader.bool()
					continue
				}
				case 3: {
					if (tag !== 26) {
						break
					}

					message.host = reader.string()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},

	fromJSON(object: any): BrowserConnectionInfo {
		return {
			isConnected: isSet(object.isConnected) ? globalThis.Boolean(object.isConnected) : false,
			isRemote: isSet(object.isRemote) ? globalThis.Boolean(object.isRemote) : false,
			host: isSet(object.host) ? globalThis.String(object.host) : undefined,
		}
	},

	toJSON(message: BrowserConnectionInfo): unknown {
		const obj: any = {}
		if (message.isConnected !== false) {
			obj.isConnected = message.isConnected
		}
		if (message.isRemote !== false) {
			obj.isRemote = message.isRemote
		}
		if (message.host !== undefined) {
			obj.host = message.host
		}
		return obj
	},

	create<I extends Exact<DeepPartial<BrowserConnectionInfo>, I>>(base?: I): BrowserConnectionInfo {
		return BrowserConnectionInfo.fromPartial(base ?? ({} as any))
	},
	fromPartial<I extends Exact<DeepPartial<BrowserConnectionInfo>, I>>(object: I): BrowserConnectionInfo {
		const message = createBaseBrowserConnectionInfo()
		message.isConnected = object.isConnected ?? false
		message.isRemote = object.isRemote ?? false
		message.host = object.host ?? undefined
		return message
	},
}

function createBaseBrowserConnection(): BrowserConnection {
	return { success: false, message: "", endpoint: undefined }
}

export const BrowserConnection: MessageFns<BrowserConnection> = {
	encode(message: BrowserConnection, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
		if (message.success !== false) {
			writer.uint32(8).bool(message.success)
		}
		if (message.message !== "") {
			writer.uint32(18).string(message.message)
		}
		if (message.endpoint !== undefined) {
			writer.uint32(26).string(message.endpoint)
		}
		return writer
	},

	decode(input: BinaryReader | Uint8Array, length?: number): BrowserConnection {
		const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseBrowserConnection()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 8) {
						break
					}

					message.success = reader.bool()
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}

					message.message = reader.string()
					continue
				}
				case 3: {
					if (tag !== 26) {
						break
					}

					message.endpoint = reader.string()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},

	fromJSON(object: any): BrowserConnection {
		return {
			success: isSet(object.success) ? globalThis.Boolean(object.success) : false,
			message: isSet(object.message) ? globalThis.String(object.message) : "",
			endpoint: isSet(object.endpoint) ? globalThis.String(object.endpoint) : undefined,
		}
	},

	toJSON(message: BrowserConnection): unknown {
		const obj: any = {}
		if (message.success !== false) {
			obj.success = message.success
		}
		if (message.message !== "") {
			obj.message = message.message
		}
		if (message.endpoint !== undefined) {
			obj.endpoint = message.endpoint
		}
		return obj
	},

	create<I extends Exact<DeepPartial<BrowserConnection>, I>>(base?: I): BrowserConnection {
		return BrowserConnection.fromPartial(base ?? ({} as any))
	},
	fromPartial<I extends Exact<DeepPartial<BrowserConnection>, I>>(object: I): BrowserConnection {
		const message = createBaseBrowserConnection()
		message.success = object.success ?? false
		message.message = object.message ?? ""
		message.endpoint = object.endpoint ?? undefined
		return message
	},
}

function createBaseChromePath(): ChromePath {
	return { path: "", isBundled: false }
}

export const ChromePath: MessageFns<ChromePath> = {
	encode(message: ChromePath, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
		if (message.path !== "") {
			writer.uint32(10).string(message.path)
		}
		if (message.isBundled !== false) {
			writer.uint32(16).bool(message.isBundled)
		}
		return writer
	},

	decode(input: BinaryReader | Uint8Array, length?: number): ChromePath {
		const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseChromePath()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}

					message.path = reader.string()
					continue
				}
				case 2: {
					if (tag !== 16) {
						break
					}

					message.isBundled = reader.bool()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},

	fromJSON(object: any): ChromePath {
		return {
			path: isSet(object.path) ? globalThis.String(object.path) : "",
			isBundled: isSet(object.isBundled) ? globalThis.Boolean(object.isBundled) : false,
		}
	},

	toJSON(message: ChromePath): unknown {
		const obj: any = {}
		if (message.path !== "") {
			obj.path = message.path
		}
		if (message.isBundled !== false) {
			obj.isBundled = message.isBundled
		}
		return obj
	},

	create<I extends Exact<DeepPartial<ChromePath>, I>>(base?: I): ChromePath {
		return ChromePath.fromPartial(base ?? ({} as any))
	},
	fromPartial<I extends Exact<DeepPartial<ChromePath>, I>>(object: I): ChromePath {
		const message = createBaseChromePath()
		message.path = object.path ?? ""
		message.isBundled = object.isBundled ?? false
		return message
	},
}

function createBaseViewport(): Viewport {
	return { width: 0, height: 0 }
}

export const Viewport: MessageFns<Viewport> = {
	encode(message: Viewport, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
		if (message.width !== 0) {
			writer.uint32(8).int32(message.width)
		}
		if (message.height !== 0) {
			writer.uint32(16).int32(message.height)
		}
		return writer
	},

	decode(input: BinaryReader | Uint8Array, length?: number): Viewport {
		const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseViewport()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 8) {
						break
					}

					message.width = reader.int32()
					continue
				}
				case 2: {
					if (tag !== 16) {
						break
					}

					message.height = reader.int32()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},

	fromJSON(object: any): Viewport {
		return {
			width: isSet(object.width) ? globalThis.Number(object.width) : 0,
			height: isSet(object.height) ? globalThis.Number(object.height) : 0,
		}
	},

	toJSON(message: Viewport): unknown {
		const obj: any = {}
		if (message.width !== 0) {
			obj.width = Math.round(message.width)
		}
		if (message.height !== 0) {
			obj.height = Math.round(message.height)
		}
		return obj
	},

	create<I extends Exact<DeepPartial<Viewport>, I>>(base?: I): Viewport {
		return Viewport.fromPartial(base ?? ({} as any))
	},
	fromPartial<I extends Exact<DeepPartial<Viewport>, I>>(object: I): Viewport {
		const message = createBaseViewport()
		message.width = object.width ?? 0
		message.height = object.height ?? 0
		return message
	},
}

function createBaseBrowserSettings(): BrowserSettings {
	return {
		viewport: undefined,
		remoteBrowserHost: undefined,
		remoteBrowserEnabled: undefined,
		chromeExecutablePath: undefined,
		disableToolUse: undefined,
	}
}

export const BrowserSettings: MessageFns<BrowserSettings> = {
	encode(message: BrowserSettings, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
		if (message.viewport !== undefined) {
			Viewport.encode(message.viewport, writer.uint32(10).fork()).join()
		}
		if (message.remoteBrowserHost !== undefined) {
			writer.uint32(18).string(message.remoteBrowserHost)
		}
		if (message.remoteBrowserEnabled !== undefined) {
			writer.uint32(24).bool(message.remoteBrowserEnabled)
		}
		if (message.chromeExecutablePath !== undefined) {
			writer.uint32(34).string(message.chromeExecutablePath)
		}
		if (message.disableToolUse !== undefined) {
			writer.uint32(40).bool(message.disableToolUse)
		}
		return writer
	},

	decode(input: BinaryReader | Uint8Array, length?: number): BrowserSettings {
		const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseBrowserSettings()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}

					message.viewport = Viewport.decode(reader, reader.uint32())
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}

					message.remoteBrowserHost = reader.string()
					continue
				}
				case 3: {
					if (tag !== 24) {
						break
					}

					message.remoteBrowserEnabled = reader.bool()
					continue
				}
				case 4: {
					if (tag !== 34) {
						break
					}

					message.chromeExecutablePath = reader.string()
					continue
				}
				case 5: {
					if (tag !== 40) {
						break
					}

					message.disableToolUse = reader.bool()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},

	fromJSON(object: any): BrowserSettings {
		return {
			viewport: isSet(object.viewport) ? Viewport.fromJSON(object.viewport) : undefined,
			remoteBrowserHost: isSet(object.remoteBrowserHost) ? globalThis.String(object.remoteBrowserHost) : undefined,
			remoteBrowserEnabled: isSet(object.remoteBrowserEnabled)
				? globalThis.Boolean(object.remoteBrowserEnabled)
				: undefined,
			chromeExecutablePath: isSet(object.chromeExecutablePath) ? globalThis.String(object.chromeExecutablePath) : undefined,
			disableToolUse: isSet(object.disableToolUse) ? globalThis.Boolean(object.disableToolUse) : undefined,
		}
	},

	toJSON(message: BrowserSettings): unknown {
		const obj: any = {}
		if (message.viewport !== undefined) {
			obj.viewport = Viewport.toJSON(message.viewport)
		}
		if (message.remoteBrowserHost !== undefined) {
			obj.remoteBrowserHost = message.remoteBrowserHost
		}
		if (message.remoteBrowserEnabled !== undefined) {
			obj.remoteBrowserEnabled = message.remoteBrowserEnabled
		}
		if (message.chromeExecutablePath !== undefined) {
			obj.chromeExecutablePath = message.chromeExecutablePath
		}
		if (message.disableToolUse !== undefined) {
			obj.disableToolUse = message.disableToolUse
		}
		return obj
	},

	create<I extends Exact<DeepPartial<BrowserSettings>, I>>(base?: I): BrowserSettings {
		return BrowserSettings.fromPartial(base ?? ({} as any))
	},
	fromPartial<I extends Exact<DeepPartial<BrowserSettings>, I>>(object: I): BrowserSettings {
		const message = createBaseBrowserSettings()
		message.viewport =
			object.viewport !== undefined && object.viewport !== null ? Viewport.fromPartial(object.viewport) : undefined
		message.remoteBrowserHost = object.remoteBrowserHost ?? undefined
		message.remoteBrowserEnabled = object.remoteBrowserEnabled ?? undefined
		message.chromeExecutablePath = object.chromeExecutablePath ?? undefined
		message.disableToolUse = object.disableToolUse ?? undefined
		return message
	},
}

function createBaseUpdateBrowserSettingsRequest(): UpdateBrowserSettingsRequest {
	return {
		metadata: undefined,
		viewport: undefined,
		remoteBrowserHost: undefined,
		remoteBrowserEnabled: undefined,
		chromeExecutablePath: undefined,
		disableToolUse: undefined,
	}
}

export const UpdateBrowserSettingsRequest: MessageFns<UpdateBrowserSettingsRequest> = {
	encode(message: UpdateBrowserSettingsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
		if (message.metadata !== undefined) {
			Metadata.encode(message.metadata, writer.uint32(10).fork()).join()
		}
		if (message.viewport !== undefined) {
			Viewport.encode(message.viewport, writer.uint32(18).fork()).join()
		}
		if (message.remoteBrowserHost !== undefined) {
			writer.uint32(26).string(message.remoteBrowserHost)
		}
		if (message.remoteBrowserEnabled !== undefined) {
			writer.uint32(32).bool(message.remoteBrowserEnabled)
		}
		if (message.chromeExecutablePath !== undefined) {
			writer.uint32(42).string(message.chromeExecutablePath)
		}
		if (message.disableToolUse !== undefined) {
			writer.uint32(48).bool(message.disableToolUse)
		}
		return writer
	},

	decode(input: BinaryReader | Uint8Array, length?: number): UpdateBrowserSettingsRequest {
		const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseUpdateBrowserSettingsRequest()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}

					message.metadata = Metadata.decode(reader, reader.uint32())
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}

					message.viewport = Viewport.decode(reader, reader.uint32())
					continue
				}
				case 3: {
					if (tag !== 26) {
						break
					}

					message.remoteBrowserHost = reader.string()
					continue
				}
				case 4: {
					if (tag !== 32) {
						break
					}

					message.remoteBrowserEnabled = reader.bool()
					continue
				}
				case 5: {
					if (tag !== 42) {
						break
					}

					message.chromeExecutablePath = reader.string()
					continue
				}
				case 6: {
					if (tag !== 48) {
						break
					}

					message.disableToolUse = reader.bool()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},

	fromJSON(object: any): UpdateBrowserSettingsRequest {
		return {
			metadata: isSet(object.metadata) ? Metadata.fromJSON(object.metadata) : undefined,
			viewport: isSet(object.viewport) ? Viewport.fromJSON(object.viewport) : undefined,
			remoteBrowserHost: isSet(object.remoteBrowserHost) ? globalThis.String(object.remoteBrowserHost) : undefined,
			remoteBrowserEnabled: isSet(object.remoteBrowserEnabled)
				? globalThis.Boolean(object.remoteBrowserEnabled)
				: undefined,
			chromeExecutablePath: isSet(object.chromeExecutablePath) ? globalThis.String(object.chromeExecutablePath) : undefined,
			disableToolUse: isSet(object.disableToolUse) ? globalThis.Boolean(object.disableToolUse) : undefined,
		}
	},

	toJSON(message: UpdateBrowserSettingsRequest): unknown {
		const obj: any = {}
		if (message.metadata !== undefined) {
			obj.metadata = Metadata.toJSON(message.metadata)
		}
		if (message.viewport !== undefined) {
			obj.viewport = Viewport.toJSON(message.viewport)
		}
		if (message.remoteBrowserHost !== undefined) {
			obj.remoteBrowserHost = message.remoteBrowserHost
		}
		if (message.remoteBrowserEnabled !== undefined) {
			obj.remoteBrowserEnabled = message.remoteBrowserEnabled
		}
		if (message.chromeExecutablePath !== undefined) {
			obj.chromeExecutablePath = message.chromeExecutablePath
		}
		if (message.disableToolUse !== undefined) {
			obj.disableToolUse = message.disableToolUse
		}
		return obj
	},

	create<I extends Exact<DeepPartial<UpdateBrowserSettingsRequest>, I>>(base?: I): UpdateBrowserSettingsRequest {
		return UpdateBrowserSettingsRequest.fromPartial(base ?? ({} as any))
	},
	fromPartial<I extends Exact<DeepPartial<UpdateBrowserSettingsRequest>, I>>(object: I): UpdateBrowserSettingsRequest {
		const message = createBaseUpdateBrowserSettingsRequest()
		message.metadata =
			object.metadata !== undefined && object.metadata !== null ? Metadata.fromPartial(object.metadata) : undefined
		message.viewport =
			object.viewport !== undefined && object.viewport !== null ? Viewport.fromPartial(object.viewport) : undefined
		message.remoteBrowserHost = object.remoteBrowserHost ?? undefined
		message.remoteBrowserEnabled = object.remoteBrowserEnabled ?? undefined
		message.chromeExecutablePath = object.chromeExecutablePath ?? undefined
		message.disableToolUse = object.disableToolUse ?? undefined
		return message
	},
}

export type BrowserServiceDefinition = typeof BrowserServiceDefinition
export const BrowserServiceDefinition = {
	name: "BrowserService",
	fullName: "cline.BrowserService",
	methods: {
		getBrowserConnectionInfo: {
			name: "getBrowserConnectionInfo",
			requestType: EmptyRequest,
			requestStream: false,
			responseType: BrowserConnectionInfo,
			responseStream: false,
			options: {},
		},
		testBrowserConnection: {
			name: "testBrowserConnection",
			requestType: StringRequest,
			requestStream: false,
			responseType: BrowserConnection,
			responseStream: false,
			options: {},
		},
		discoverBrowser: {
			name: "discoverBrowser",
			requestType: EmptyRequest,
			requestStream: false,
			responseType: BrowserConnection,
			responseStream: false,
			options: {},
		},
		getDetectedChromePath: {
			name: "getDetectedChromePath",
			requestType: EmptyRequest,
			requestStream: false,
			responseType: ChromePath,
			responseStream: false,
			options: {},
		},
		updateBrowserSettings: {
			name: "updateBrowserSettings",
			requestType: UpdateBrowserSettingsRequest,
			requestStream: false,
			responseType: Boolean,
			responseStream: false,
			options: {},
		},
	},
} as const

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined

export type DeepPartial<T> = T extends Builtin
	? T
	: T extends globalThis.Array<infer U>
		? globalThis.Array<DeepPartial<U>>
		: T extends ReadonlyArray<infer U>
			? ReadonlyArray<DeepPartial<U>>
			: T extends {}
				? { [K in keyof T]?: DeepPartial<T[K]> }
				: Partial<T>

type KeysOfUnion<T> = T extends T ? keyof T : never
export type Exact<P, I extends P> = P extends Builtin
	? P
	: P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never }

function isSet(value: any): boolean {
	return value !== null && value !== undefined
}

export interface MessageFns<T> {
	encode(message: T, writer?: BinaryWriter): BinaryWriter
	decode(input: BinaryReader | Uint8Array, length?: number): T
	fromJSON(object: any): T
	toJSON(message: T): unknown
	create<I extends Exact<DeepPartial<T>, I>>(base?: I): T
	fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T
}
