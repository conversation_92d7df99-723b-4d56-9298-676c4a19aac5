// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.19.1
// source: models.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire"
import { EmptyRequest, Metadata, StringArray, StringRequest } from "./common"

export const protobufPackage = "cline"

/** List of VS Code LM models */
export interface VsCodeLmModelsArray {
	models: VsCodeLmModel[]
}

/** Structure representing a VS Code LM model */
export interface VsCodeLmModel {
	vendor: string
	family: string
	version: string
	id: string
}

/** For OpenRouterCompatibleModelInfo structure in OpenRouterModels */
export interface OpenRouterModelInfo {
	maxTokens: number
	contextWindow: number
	supportsImages: boolean
	supportsPromptCache: boolean
	inputPrice: number
	outputPrice: number
	cacheWritesPrice: number
	cacheReadsPrice: number
	description: string
}

/** Shared response message for model information */
export interface OpenRouterCompatibleModelInfo {
	models: { [key: string]: OpenRouterModelInfo }
}

export interface OpenRouterCompatibleModelInfo_ModelsEntry {
	key: string
	value?: OpenRouterModelInfo | undefined
}

/** Request for fetching OpenAI models */
export interface OpenAiModelsRequest {
	metadata?: Metadata | undefined
	baseUrl: string
	apiKey: string
}

function createBaseVsCodeLmModelsArray(): VsCodeLmModelsArray {
	return { models: [] }
}

export const VsCodeLmModelsArray: MessageFns<VsCodeLmModelsArray> = {
	encode(message: VsCodeLmModelsArray, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
		for (const v of message.models) {
			VsCodeLmModel.encode(v!, writer.uint32(10).fork()).join()
		}
		return writer
	},

	decode(input: BinaryReader | Uint8Array, length?: number): VsCodeLmModelsArray {
		const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseVsCodeLmModelsArray()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}

					message.models.push(VsCodeLmModel.decode(reader, reader.uint32()))
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},

	fromJSON(object: any): VsCodeLmModelsArray {
		return {
			models: globalThis.Array.isArray(object?.models) ? object.models.map((e: any) => VsCodeLmModel.fromJSON(e)) : [],
		}
	},

	toJSON(message: VsCodeLmModelsArray): unknown {
		const obj: any = {}
		if (message.models?.length) {
			obj.models = message.models.map((e) => VsCodeLmModel.toJSON(e))
		}
		return obj
	},

	create<I extends Exact<DeepPartial<VsCodeLmModelsArray>, I>>(base?: I): VsCodeLmModelsArray {
		return VsCodeLmModelsArray.fromPartial(base ?? ({} as any))
	},
	fromPartial<I extends Exact<DeepPartial<VsCodeLmModelsArray>, I>>(object: I): VsCodeLmModelsArray {
		const message = createBaseVsCodeLmModelsArray()
		message.models = object.models?.map((e) => VsCodeLmModel.fromPartial(e)) || []
		return message
	},
}

function createBaseVsCodeLmModel(): VsCodeLmModel {
	return { vendor: "", family: "", version: "", id: "" }
}

export const VsCodeLmModel: MessageFns<VsCodeLmModel> = {
	encode(message: VsCodeLmModel, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
		if (message.vendor !== "") {
			writer.uint32(10).string(message.vendor)
		}
		if (message.family !== "") {
			writer.uint32(18).string(message.family)
		}
		if (message.version !== "") {
			writer.uint32(26).string(message.version)
		}
		if (message.id !== "") {
			writer.uint32(34).string(message.id)
		}
		return writer
	},

	decode(input: BinaryReader | Uint8Array, length?: number): VsCodeLmModel {
		const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseVsCodeLmModel()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}

					message.vendor = reader.string()
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}

					message.family = reader.string()
					continue
				}
				case 3: {
					if (tag !== 26) {
						break
					}

					message.version = reader.string()
					continue
				}
				case 4: {
					if (tag !== 34) {
						break
					}

					message.id = reader.string()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},

	fromJSON(object: any): VsCodeLmModel {
		return {
			vendor: isSet(object.vendor) ? globalThis.String(object.vendor) : "",
			family: isSet(object.family) ? globalThis.String(object.family) : "",
			version: isSet(object.version) ? globalThis.String(object.version) : "",
			id: isSet(object.id) ? globalThis.String(object.id) : "",
		}
	},

	toJSON(message: VsCodeLmModel): unknown {
		const obj: any = {}
		if (message.vendor !== "") {
			obj.vendor = message.vendor
		}
		if (message.family !== "") {
			obj.family = message.family
		}
		if (message.version !== "") {
			obj.version = message.version
		}
		if (message.id !== "") {
			obj.id = message.id
		}
		return obj
	},

	create<I extends Exact<DeepPartial<VsCodeLmModel>, I>>(base?: I): VsCodeLmModel {
		return VsCodeLmModel.fromPartial(base ?? ({} as any))
	},
	fromPartial<I extends Exact<DeepPartial<VsCodeLmModel>, I>>(object: I): VsCodeLmModel {
		const message = createBaseVsCodeLmModel()
		message.vendor = object.vendor ?? ""
		message.family = object.family ?? ""
		message.version = object.version ?? ""
		message.id = object.id ?? ""
		return message
	},
}

function createBaseOpenRouterModelInfo(): OpenRouterModelInfo {
	return {
		maxTokens: 0,
		contextWindow: 0,
		supportsImages: false,
		supportsPromptCache: false,
		inputPrice: 0,
		outputPrice: 0,
		cacheWritesPrice: 0,
		cacheReadsPrice: 0,
		description: "",
	}
}

export const OpenRouterModelInfo: MessageFns<OpenRouterModelInfo> = {
	encode(message: OpenRouterModelInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
		if (message.maxTokens !== 0) {
			writer.uint32(8).int32(message.maxTokens)
		}
		if (message.contextWindow !== 0) {
			writer.uint32(16).int32(message.contextWindow)
		}
		if (message.supportsImages !== false) {
			writer.uint32(24).bool(message.supportsImages)
		}
		if (message.supportsPromptCache !== false) {
			writer.uint32(32).bool(message.supportsPromptCache)
		}
		if (message.inputPrice !== 0) {
			writer.uint32(41).double(message.inputPrice)
		}
		if (message.outputPrice !== 0) {
			writer.uint32(49).double(message.outputPrice)
		}
		if (message.cacheWritesPrice !== 0) {
			writer.uint32(57).double(message.cacheWritesPrice)
		}
		if (message.cacheReadsPrice !== 0) {
			writer.uint32(65).double(message.cacheReadsPrice)
		}
		if (message.description !== "") {
			writer.uint32(74).string(message.description)
		}
		return writer
	},

	decode(input: BinaryReader | Uint8Array, length?: number): OpenRouterModelInfo {
		const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseOpenRouterModelInfo()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 8) {
						break
					}

					message.maxTokens = reader.int32()
					continue
				}
				case 2: {
					if (tag !== 16) {
						break
					}

					message.contextWindow = reader.int32()
					continue
				}
				case 3: {
					if (tag !== 24) {
						break
					}

					message.supportsImages = reader.bool()
					continue
				}
				case 4: {
					if (tag !== 32) {
						break
					}

					message.supportsPromptCache = reader.bool()
					continue
				}
				case 5: {
					if (tag !== 41) {
						break
					}

					message.inputPrice = reader.double()
					continue
				}
				case 6: {
					if (tag !== 49) {
						break
					}

					message.outputPrice = reader.double()
					continue
				}
				case 7: {
					if (tag !== 57) {
						break
					}

					message.cacheWritesPrice = reader.double()
					continue
				}
				case 8: {
					if (tag !== 65) {
						break
					}

					message.cacheReadsPrice = reader.double()
					continue
				}
				case 9: {
					if (tag !== 74) {
						break
					}

					message.description = reader.string()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},

	fromJSON(object: any): OpenRouterModelInfo {
		return {
			maxTokens: isSet(object.maxTokens) ? globalThis.Number(object.maxTokens) : 0,
			contextWindow: isSet(object.contextWindow) ? globalThis.Number(object.contextWindow) : 0,
			supportsImages: isSet(object.supportsImages) ? globalThis.Boolean(object.supportsImages) : false,
			supportsPromptCache: isSet(object.supportsPromptCache) ? globalThis.Boolean(object.supportsPromptCache) : false,
			inputPrice: isSet(object.inputPrice) ? globalThis.Number(object.inputPrice) : 0,
			outputPrice: isSet(object.outputPrice) ? globalThis.Number(object.outputPrice) : 0,
			cacheWritesPrice: isSet(object.cacheWritesPrice) ? globalThis.Number(object.cacheWritesPrice) : 0,
			cacheReadsPrice: isSet(object.cacheReadsPrice) ? globalThis.Number(object.cacheReadsPrice) : 0,
			description: isSet(object.description) ? globalThis.String(object.description) : "",
		}
	},

	toJSON(message: OpenRouterModelInfo): unknown {
		const obj: any = {}
		if (message.maxTokens !== 0) {
			obj.maxTokens = Math.round(message.maxTokens)
		}
		if (message.contextWindow !== 0) {
			obj.contextWindow = Math.round(message.contextWindow)
		}
		if (message.supportsImages !== false) {
			obj.supportsImages = message.supportsImages
		}
		if (message.supportsPromptCache !== false) {
			obj.supportsPromptCache = message.supportsPromptCache
		}
		if (message.inputPrice !== 0) {
			obj.inputPrice = message.inputPrice
		}
		if (message.outputPrice !== 0) {
			obj.outputPrice = message.outputPrice
		}
		if (message.cacheWritesPrice !== 0) {
			obj.cacheWritesPrice = message.cacheWritesPrice
		}
		if (message.cacheReadsPrice !== 0) {
			obj.cacheReadsPrice = message.cacheReadsPrice
		}
		if (message.description !== "") {
			obj.description = message.description
		}
		return obj
	},

	create<I extends Exact<DeepPartial<OpenRouterModelInfo>, I>>(base?: I): OpenRouterModelInfo {
		return OpenRouterModelInfo.fromPartial(base ?? ({} as any))
	},
	fromPartial<I extends Exact<DeepPartial<OpenRouterModelInfo>, I>>(object: I): OpenRouterModelInfo {
		const message = createBaseOpenRouterModelInfo()
		message.maxTokens = object.maxTokens ?? 0
		message.contextWindow = object.contextWindow ?? 0
		message.supportsImages = object.supportsImages ?? false
		message.supportsPromptCache = object.supportsPromptCache ?? false
		message.inputPrice = object.inputPrice ?? 0
		message.outputPrice = object.outputPrice ?? 0
		message.cacheWritesPrice = object.cacheWritesPrice ?? 0
		message.cacheReadsPrice = object.cacheReadsPrice ?? 0
		message.description = object.description ?? ""
		return message
	},
}

function createBaseOpenRouterCompatibleModelInfo(): OpenRouterCompatibleModelInfo {
	return { models: {} }
}

export const OpenRouterCompatibleModelInfo: MessageFns<OpenRouterCompatibleModelInfo> = {
	encode(message: OpenRouterCompatibleModelInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
		Object.entries(message.models).forEach(([key, value]) => {
			OpenRouterCompatibleModelInfo_ModelsEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join()
		})
		return writer
	},

	decode(input: BinaryReader | Uint8Array, length?: number): OpenRouterCompatibleModelInfo {
		const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseOpenRouterCompatibleModelInfo()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}

					const entry1 = OpenRouterCompatibleModelInfo_ModelsEntry.decode(reader, reader.uint32())
					if (entry1.value !== undefined) {
						message.models[entry1.key] = entry1.value
					}
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},

	fromJSON(object: any): OpenRouterCompatibleModelInfo {
		return {
			models: isObject(object.models)
				? Object.entries(object.models).reduce<{ [key: string]: OpenRouterModelInfo }>((acc, [key, value]) => {
						acc[key] = OpenRouterModelInfo.fromJSON(value)
						return acc
					}, {})
				: {},
		}
	},

	toJSON(message: OpenRouterCompatibleModelInfo): unknown {
		const obj: any = {}
		if (message.models) {
			const entries = Object.entries(message.models)
			if (entries.length > 0) {
				obj.models = {}
				entries.forEach(([k, v]) => {
					obj.models[k] = OpenRouterModelInfo.toJSON(v)
				})
			}
		}
		return obj
	},

	create<I extends Exact<DeepPartial<OpenRouterCompatibleModelInfo>, I>>(base?: I): OpenRouterCompatibleModelInfo {
		return OpenRouterCompatibleModelInfo.fromPartial(base ?? ({} as any))
	},
	fromPartial<I extends Exact<DeepPartial<OpenRouterCompatibleModelInfo>, I>>(object: I): OpenRouterCompatibleModelInfo {
		const message = createBaseOpenRouterCompatibleModelInfo()
		message.models = Object.entries(object.models ?? {}).reduce<{ [key: string]: OpenRouterModelInfo }>(
			(acc, [key, value]) => {
				if (value !== undefined) {
					acc[key] = OpenRouterModelInfo.fromPartial(value)
				}
				return acc
			},
			{},
		)
		return message
	},
}

function createBaseOpenRouterCompatibleModelInfo_ModelsEntry(): OpenRouterCompatibleModelInfo_ModelsEntry {
	return { key: "", value: undefined }
}

export const OpenRouterCompatibleModelInfo_ModelsEntry: MessageFns<OpenRouterCompatibleModelInfo_ModelsEntry> = {
	encode(message: OpenRouterCompatibleModelInfo_ModelsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
		if (message.key !== "") {
			writer.uint32(10).string(message.key)
		}
		if (message.value !== undefined) {
			OpenRouterModelInfo.encode(message.value, writer.uint32(18).fork()).join()
		}
		return writer
	},

	decode(input: BinaryReader | Uint8Array, length?: number): OpenRouterCompatibleModelInfo_ModelsEntry {
		const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseOpenRouterCompatibleModelInfo_ModelsEntry()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}

					message.key = reader.string()
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}

					message.value = OpenRouterModelInfo.decode(reader, reader.uint32())
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},

	fromJSON(object: any): OpenRouterCompatibleModelInfo_ModelsEntry {
		return {
			key: isSet(object.key) ? globalThis.String(object.key) : "",
			value: isSet(object.value) ? OpenRouterModelInfo.fromJSON(object.value) : undefined,
		}
	},

	toJSON(message: OpenRouterCompatibleModelInfo_ModelsEntry): unknown {
		const obj: any = {}
		if (message.key !== "") {
			obj.key = message.key
		}
		if (message.value !== undefined) {
			obj.value = OpenRouterModelInfo.toJSON(message.value)
		}
		return obj
	},

	create<I extends Exact<DeepPartial<OpenRouterCompatibleModelInfo_ModelsEntry>, I>>(
		base?: I,
	): OpenRouterCompatibleModelInfo_ModelsEntry {
		return OpenRouterCompatibleModelInfo_ModelsEntry.fromPartial(base ?? ({} as any))
	},
	fromPartial<I extends Exact<DeepPartial<OpenRouterCompatibleModelInfo_ModelsEntry>, I>>(
		object: I,
	): OpenRouterCompatibleModelInfo_ModelsEntry {
		const message = createBaseOpenRouterCompatibleModelInfo_ModelsEntry()
		message.key = object.key ?? ""
		message.value =
			object.value !== undefined && object.value !== null ? OpenRouterModelInfo.fromPartial(object.value) : undefined
		return message
	},
}

function createBaseOpenAiModelsRequest(): OpenAiModelsRequest {
	return { metadata: undefined, baseUrl: "", apiKey: "" }
}

export const OpenAiModelsRequest: MessageFns<OpenAiModelsRequest> = {
	encode(message: OpenAiModelsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
		if (message.metadata !== undefined) {
			Metadata.encode(message.metadata, writer.uint32(10).fork()).join()
		}
		if (message.baseUrl !== "") {
			writer.uint32(18).string(message.baseUrl)
		}
		if (message.apiKey !== "") {
			writer.uint32(26).string(message.apiKey)
		}
		return writer
	},

	decode(input: BinaryReader | Uint8Array, length?: number): OpenAiModelsRequest {
		const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseOpenAiModelsRequest()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}

					message.metadata = Metadata.decode(reader, reader.uint32())
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}

					message.baseUrl = reader.string()
					continue
				}
				case 3: {
					if (tag !== 26) {
						break
					}

					message.apiKey = reader.string()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},

	fromJSON(object: any): OpenAiModelsRequest {
		return {
			metadata: isSet(object.metadata) ? Metadata.fromJSON(object.metadata) : undefined,
			baseUrl: isSet(object.baseUrl) ? globalThis.String(object.baseUrl) : "",
			apiKey: isSet(object.apiKey) ? globalThis.String(object.apiKey) : "",
		}
	},

	toJSON(message: OpenAiModelsRequest): unknown {
		const obj: any = {}
		if (message.metadata !== undefined) {
			obj.metadata = Metadata.toJSON(message.metadata)
		}
		if (message.baseUrl !== "") {
			obj.baseUrl = message.baseUrl
		}
		if (message.apiKey !== "") {
			obj.apiKey = message.apiKey
		}
		return obj
	},

	create<I extends Exact<DeepPartial<OpenAiModelsRequest>, I>>(base?: I): OpenAiModelsRequest {
		return OpenAiModelsRequest.fromPartial(base ?? ({} as any))
	},
	fromPartial<I extends Exact<DeepPartial<OpenAiModelsRequest>, I>>(object: I): OpenAiModelsRequest {
		const message = createBaseOpenAiModelsRequest()
		message.metadata =
			object.metadata !== undefined && object.metadata !== null ? Metadata.fromPartial(object.metadata) : undefined
		message.baseUrl = object.baseUrl ?? ""
		message.apiKey = object.apiKey ?? ""
		return message
	},
}

/** Service for model-related operations */
export type ModelsServiceDefinition = typeof ModelsServiceDefinition
export const ModelsServiceDefinition = {
	name: "ModelsService",
	fullName: "cline.ModelsService",
	methods: {
		/** Fetches available models from Ollama */
		getOllamaModels: {
			name: "getOllamaModels",
			requestType: StringRequest,
			requestStream: false,
			responseType: StringArray,
			responseStream: false,
			options: {},
		},
		/** Fetches available models from LM Studio */
		getLmStudioModels: {
			name: "getLmStudioModels",
			requestType: StringRequest,
			requestStream: false,
			responseType: StringArray,
			responseStream: false,
			options: {},
		},
		/** Fetches available models from VS Code LM API */
		getVsCodeLmModels: {
			name: "getVsCodeLmModels",
			requestType: EmptyRequest,
			requestStream: false,
			responseType: VsCodeLmModelsArray,
			responseStream: false,
			options: {},
		},
		/** Refreshes and returns OpenRouter models */
		refreshOpenRouterModels: {
			name: "refreshOpenRouterModels",
			requestType: EmptyRequest,
			requestStream: false,
			responseType: OpenRouterCompatibleModelInfo,
			responseStream: false,
			options: {},
		},
		/** Refreshes and returns OpenAI models */
		refreshOpenAiModels: {
			name: "refreshOpenAiModels",
			requestType: OpenAiModelsRequest,
			requestStream: false,
			responseType: StringArray,
			responseStream: false,
			options: {},
		},
		/** Refreshes and returns Requesty models */
		refreshRequestyModels: {
			name: "refreshRequestyModels",
			requestType: EmptyRequest,
			requestStream: false,
			responseType: OpenRouterCompatibleModelInfo,
			responseStream: false,
			options: {},
		},
	},
} as const

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined

export type DeepPartial<T> = T extends Builtin
	? T
	: T extends globalThis.Array<infer U>
		? globalThis.Array<DeepPartial<U>>
		: T extends ReadonlyArray<infer U>
			? ReadonlyArray<DeepPartial<U>>
			: T extends {}
				? { [K in keyof T]?: DeepPartial<T[K]> }
				: Partial<T>

type KeysOfUnion<T> = T extends T ? keyof T : never
export type Exact<P, I extends P> = P extends Builtin
	? P
	: P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never }

function isObject(value: any): boolean {
	return typeof value === "object" && value !== null
}

function isSet(value: any): boolean {
	return value !== null && value !== undefined
}

export interface MessageFns<T> {
	encode(message: T, writer?: BinaryWriter): BinaryWriter
	decode(input: BinaryReader | Uint8Array, length?: number): T
	fromJSON(object: any): T
	toJSON(message: T): unknown
	create<I extends Exact<DeepPartial<T>, I>>(base?: I): T
	fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T
}
