// AUTO-GENERATED FILE - DO NOT MODIFY DIRECTLY
// Generated by proto/build-proto.js

import { createGrpcClient } from "./grpc-client-base"
import { AccountServiceDefinition } from "@shared/proto/account"
import { BrowserServiceDefinition } from "@shared/proto/browser"
import { CheckpointsServiceDefinition } from "@shared/proto/checkpoints"
import { FileServiceDefinition } from "@shared/proto/file"
import { McpServiceDefinition } from "@shared/proto/mcp"
import { StateServiceDefinition } from "@shared/proto/state"
import { TaskServiceDefinition } from "@shared/proto/task"
import { WebServiceDefinition } from "@shared/proto/web"
import { ModelsServiceDefinition } from "@shared/proto/models"
import { SlashServiceDefinition } from "@shared/proto/slash"

const AccountServiceClient = createGrpcClient(AccountServiceDefinition)
const BrowserServiceClient = createGrpcClient(BrowserServiceDefinition)
const CheckpointsServiceClient = createGrpcClient(CheckpointsServiceDefinition)
const FileServiceClient = createGrpcClient(FileServiceDefinition)
const McpServiceClient = createGrpcClient(McpServiceDefinition)
const StateServiceClient = createGrpcClient(StateServiceDefinition)
const TaskServiceClient = createGrpcClient(TaskServiceDefinition)
const WebServiceClient = createGrpcClient(WebServiceDefinition)
const ModelsServiceClient = createGrpcClient(ModelsServiceDefinition)
const SlashServiceClient = createGrpcClient(SlashServiceDefinition)

export {
	AccountServiceClient,
	BrowserServiceClient,
	CheckpointsServiceClient,
	FileServiceClient,
	McpServiceClient,
	StateServiceClient,
	TaskServiceClient,
	WebServiceClient,
	ModelsServiceClient,
	SlashServiceClient,
}
