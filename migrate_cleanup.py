#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to move migrated JavaScript files to a backup directory.
This script identifies JavaScript components that have been migrated to Python
and moves them to a backup directory instead of deleting them outright.
"""
import os
import shutil
from pathlib import Path
import argparse

# Define the migrated components
MIGRATED_COMPONENTS = [
    # Core components
    "core/task",
    "core/prompts",
    "core/controller",
    
    # Integrations
    "integrations/workspace",
    "integrations/git",
    
    # Services
    "services/telemetry",
    "services/api",
]

def backup_migrated_files(src_dir, backup_dir, dry_run=True):
    """
    Move migrated JavaScript files to a backup directory.
    
    Args:
        src_dir: Source directory containing the JavaScript files
        backup_dir: Backup directory to move files to
        dry_run: If True, only print what would be done without actually moving files
    """
    src_path = Path(src_dir)
    backup_path = Path(backup_dir)
    
    # Ensure backup directory exists
    if not dry_run:
        backup_path.mkdir(parents=True, exist_ok=True)
    
    moved_files = 0
    
    # Process each migrated component
    for component in MIGRATED_COMPONENTS:
        component_path = src_path / component
        
        if not component_path.exists():
            print(f"Component path not found: {component_path}")
            continue
        
        # Create corresponding backup directory
        component_backup_path = backup_path / component
        if not dry_run:
            component_backup_path.mkdir(parents=True, exist_ok=True)
        
        # Find all TypeScript/JavaScript files in the component directory
        js_files = list(component_path.glob("**/*.ts")) + list(component_path.glob("**/*.js"))
        
        for js_file in js_files:
            # Get the relative path from the component directory
            rel_path = js_file.relative_to(component_path)
            backup_file = component_backup_path / rel_path
            
            # Create parent directories if needed
            if not dry_run:
                backup_file.parent.mkdir(parents=True, exist_ok=True)
            
            print(f"Moving: {js_file} -> {backup_file}")
            
            if not dry_run:
                try:
                    shutil.move(str(js_file), str(backup_file))
                    moved_files += 1
                except Exception as e:
                    print(f"Error moving {js_file}: {e}")
    
    print(f"\nTotal files {'that would be' if dry_run else ''} moved: {moved_files}")
    
    if dry_run:
        print("\nThis was a dry run. No files were actually moved.")
        print("Run with --execute to actually move the files.")

def main():
    parser = argparse.ArgumentParser(description="Move migrated JavaScript files to a backup directory")
    parser.add_argument("--src", default="./src", help="Source directory containing JavaScript files")
    parser.add_argument("--backup", default="./js-backup", help="Backup directory to move files to")
    parser.add_argument("--execute", action="store_true", help="Actually move files (default is dry run)")
    
    args = parser.parse_args()
    
    backup_migrated_files(args.src, args.backup, not args.execute)

if __name__ == "__main__":
    main()
