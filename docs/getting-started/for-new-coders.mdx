---
title: "For New Coders"
description: "Welcome to Cline, your AI-powered coding companion! This guide will help you quickly set up your development environment and begin your coding journey with ease."
---

> 💡 **Tip:** If you're completely new to coding, take your time with each step. There's no rush — <PERSON><PERSON> is here to guide you!

### 🚀 Getting Started

Before you jump into coding, make sure you have these essentials ready:

#### 1. **VS Code**

A popular, free, and powerful code editor.

-   [Download VS Code](https://code.visualstudio.com/)

📺 **Recommended YouTube Tutorial:** [How to Install VS Code](https://www.youtube.com/watch?v=MlIzFUI1QGA)

> ✅ **Pro Tip:** Install VS Code in your Applications folder (macOS) or Program Files (Windows) for easy access from your dock or start menu.

#### 2. **Essential Development Tools**

Basic software required for coding efficiently:

-   Homebrew (macOS)
-   Node.js
-   Git

👉 Follow our detailed guide on Installing Essential Development Tools with step-by-step help from <PERSON><PERSON>.

📺 **Recommended YouTube Tutorials:**

-   **For macOS:**
    -   [Install Homebrew on Mac](https://www.youtube.com/watch?v=hwGNgVbqasc)
    -   [Install Git on MacOS 2024](https://www.youtube.com/watch?v=B4qsvQ5IqWk)
    -   [Install Node.js on Mac (M1 | M2 | M3)](https://www.youtube.com/watch?v=I8H4wolRFBk)
-   **For Windows:**
    -   [Install Git on Windows 10/11 (2024)](https://www.youtube.com/watch?v=yjxv1HuRQy0)
    -   [Install Node.js in Windows 10/11](https://www.youtube.com/watch?v=uCgAuOYpJd0)

> ⚠️ **Note:** If you run into permission issues during installation, try running your terminal or command prompt as an administrator.

#### 3. **Organize Your Projects**

Create a dedicated folder named `Cline` in your Documents folder for all your coding projects:

-   **macOS:** `/Users/<USER>/Documents/Cline`
-   **Windows:** `C:\Users\<USER>\Documents\Cline`

Inside your `Cline` folder, structure projects clearly:

-   `Documents/Cline/workout-app` _(e.g., for a fitness tracking app)_
-   `Documents/Cline/portfolio-website` _(e.g., to showcase your work)_

> 💡 **Tip:** Keeping your projects organized from the start will save you time and confusion later!

#### 4. **Install the Cline VS Code Extension**

Enhance your coding workflow by installing the Cline extension directly within VS Code:

-   Get Started with Cline Extension Tutorial

📺 **Recommended YouTube Tutorial:** [How To Install Extensions in VS Code](https://www.youtube.com/watch?v=E7trgwZa-mk)

> ✅ **Pro Tip:** After installing, reload VS Code to ensure the extension is activated properly.

🎉 You're all set! Dive in and start coding smarter and faster with **Cline**.
