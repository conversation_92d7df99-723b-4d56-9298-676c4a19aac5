---
title: "Plan & Act Modes: A Guide to Effective AI Development"
---

## Overview

Plan & Act modes represent <PERSON><PERSON>'s approach to structured AI development, emphasizing thoughtful planning before implementation. This dual-mode system helps developers create more maintainable, accurate code while reducing iteration time.

<Frame>
	<img
		src="https://storage.googleapis.com/cline_public_images/docs/assets/planningThenActing%20(1).gif"
		alt="Use Plan to gather context before using Act to implement the plan"
	/>
</Frame>

### Understanding the Modes

#### Plan Mode

-   Optimized for context gathering and strategy
-   Cannot make changes to your codebase
-   Focused on understanding requirements and creating implementation plans
-   Enables full file reading for comprehensive project understanding

#### Act Mode

-   Streamlined for implementation based on established plans
-   Has access to all of Cline's building capabilities
-   Maintains context from the planning phase
-   Can execute changes to your codebase

<Frame>
	<img src="https://storage.googleapis.com/cline_public_images/docs/assets/image%20(5).png" alt="Act mode capabilities" />
</Frame>

### Workflow Guide

#### 1. Start with Plan Mode

Begin every significant development task in Plan mode:

In this mode:

<Frame>
	<img src="https://storage.googleapis.com/cline_public_images/docs/assets/image%20(5)%20(1).png" alt="Plan mode workflow" />
</Frame>

-   Share your requirements
-   Let Cline analyze relevant files
-   Engage in dialogue to clarify objectives
-   Develop implementation strategy

<Frame>
	<img
		src="https://storage.googleapis.com/cline_public_images/docs/assets/image%20(2)%20(1)%20(1)%20(1).png"
		alt="Planning phase"
	/>
</Frame>

#### 2. Switch to Act Mode

Once you have a clear plan, switch to Act mode:

<Frame>
	<img src="https://storage.googleapis.com/cline_public_images/docs/assets/switching-to-act.gif" alt="Switching to Act mode" />
</Frame>

Act mode allows Cline to:

-   Execute against the agreed plan
-   Make changes to your codebase
-   Maintain context from planning phase

#### 3. Iterate as Needed

Complex projects often require multiple plan-act cycles:

-   Return to Plan mode when encountering unexpected complexity
-   Use Act mode for implementing solutions
-   Maintain development momentum while ensuring quality

### Best Practices

#### Planning Phase

1. Be comprehensive with requirements
2. Share relevant context upfront
3. Point Cline to relevant files if he hasn't read them
4. Validate approach before implementation

#### Implementation Phase

1. Follow the established plan
2. Monitor progress against objectives
3. Track changes and their impact
4. Document significant decisions

<Frame>
	<img
		src="https://storage.googleapis.com/cline_public_images/docs/assets/image%20(3)%20(1).png"
		alt="Implementation best practices"
	/>
</Frame>

### Power User Tips

#### Enhancing Planning

-   Use Plan mode to explore edge cases before implementation
-   Switch back to Plan when encountering unexpected complexity
-   Leverage file reading to validate assumptions early
-   Have Cline write markdown files of the plan for future reference

### Common Patterns

#### When to Use Plan Mode

-   Starting new features
-   Debugging complex issues
-   Architectural decisions
-   Requirements analysis

#### When to Use Act Mode

-   Implementing agreed solutions
-   Making routine changes
-   Following established patterns
-   Executing test cases

<Frame>
	<img src="https://storage.googleapis.com/cline_public_images/docs/assets/image%20(6).png" alt="Mode usage patterns" />
</Frame>

### Contributing

Share your experiences and improvements:

-   Join our [Discord community](https://discord.gg/cline)
-   Participate in discussions
-   Submit feature requests
-   Report issues

---

Remember: The time invested in planning pays dividends in implementation quality and maintenance efficiency
